﻿/**
 * Copyright 1998-2012 Epic Games, Inc. All Rights Reserved.
 */

using System;
using System.Collections.Generic;
using System.Text;

namespace UnrealBuildTool
{
    class UE3BuildMU2GameMobile : UE3BuildGame
    {
        /** Returns the singular name of the game being built ("ExampleGame", "UDKGame", etc) */
        public string GetGameName()
        {
            return "MU2Game";
        }

        /** Returns a subplatform (e.g. dll) to disambiguate object files */
        public string GetSubPlatform()
        {
            return ("");
        }

        /** Get the desired OnlineSubsystem. */
        public string GetDesiredOnlineSubsystem(CPPEnvironment CPPEnv, UnrealTargetPlatform Platform)
        {
            return ("PC");
        }

        /** Returns true if the game wants to have PC ES2 simulator (ie ES2 Dynamic RHI) enabled */
        public bool ShouldCompileES2()
        {
            return true;
        }

        /** Returns whether PhysX should be compiled on mobile platforms */
        public bool ShouldCompilePhysXMobile()
        {
            return UE3BuildConfiguration.bCompilePhysXWithMobile;
        }

        /** Allows the game add any global environment settings before building */
        public void GetGameSpecificGlobalEnvironment(CPPEnvironment GlobalEnvironment, UnrealTargetPlatform Platform)
        {

        }

        /** Allows the game to add any Platform/Configuration environment settings before building */
        public void GetGameSpecificPlatformConfigurationEnvironment(CPPEnvironment GlobalEnvironment, LinkEnvironment FinalLinkEnvironment)
        {

        }

        /** Returns the xex.xml file for the given game */
        public FileItem GetXEXConfigFile()
        {
            return FileItem.GetExistingItemByPath("MU2Game/Live/xex.xml");
        }

        /** Allows the game to add any additional environment settings before building */
        public void SetUpGameEnvironment(CPPEnvironment GameCPPEnvironment, LinkEnvironment FinalLinkEnvironment, List<UE3ProjectDesc> GameProjects)
        {
            GameCPPEnvironment.IncludePaths.Add("MU2Game/Inc");
            GameProjects.Add(new UE3ProjectDesc("MU2Game/MU2Game.vcxproj"));
            GameCPPEnvironment.IncludePaths.Add("../../../../Client/Public");
            //            GameCPPEnvironment.IncludePaths.Add("../../../../Client/TableManager/Inc");
            GameCPPEnvironment.IncludePaths.Add("../../../../");        // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Shared");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Shared/Scripts"); // client inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/boost");  // server inc
            GameCPPEnvironment.IncludePaths.Add("../../../../Vendor/log4cplus/include"); 

            // common lib
            FinalLinkEnvironment.AdditionalLibraries.Add("ws2_32.lib");  // socket lib
            FinalLinkEnvironment.AdditionalLibraries.Add("mswsock.lib");  // socket lib

            if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
            {
                // 회사 내부 크래시 리포터
                FinalLinkEnvironment.AdditionalLibraries.Add("../../Binaries/Win32/CrashReporter_r.lib");  // Crash Reporter lib
            }
            else
            {
                // 회사 내부 크래시 리포터
                FinalLinkEnvironment.AdditionalLibraries.Add("../../Binaries/Win64/CrashReporter_r.lib");  // Crash Reporter lib
            }

            string MathLibPath = "../../../../Shared/Lib/";
            string CoreLibPath = "../../../../Shared/Lib/";
            string SharedLibPath = "../../../../Shared/Lib/";
            string NetworkLibPath = "../../../../Shared/Lib/";
            string CryptLibPath = "../../../../Vendor/Lib/";
            string Log4LibPath = "../../../../Vendor/Lib/";

            if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
            {
                MathLibPath += "x86/";
                CoreLibPath += "x86/";
                SharedLibPath += "x86/";
                NetworkLibPath += "x86/";
                CryptLibPath += "x86/";
                Log4LibPath += "x86/";
            }
            else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
            {
                MathLibPath += "x64/";
                CoreLibPath += "x64/";
                SharedLibPath += "x64/";
                NetworkLibPath += "x64/";
                CryptLibPath += "x64/";
                Log4LibPath += "x64/";
            }

// WJC_VS2013
            if (GameCPPEnvironment.TargetConfiguration == CPPTargetConfiguration.Debug)
            {
                if (UE3BuildConfiguration.bVS2013)
                {
                    MathLibPath += "Math_2013_ClientD.lib";
                    CoreLibPath += "Core_2013_ClientD.lib";
                    SharedLibPath += "Shared_2013_ClientD.lib";
                    CryptLibPath += "cryptlib_2013_md_d.lib";
                    Log4LibPath += "log4cplusUD.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork_2013D.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_64D.lib";
                    }
                }
                else
                {
                    MathLibPath += "Math_ClientD.lib";
                    CoreLibPath += "Core_ClientD.lib";
                    SharedLibPath += "Shared_ClientD.lib";
                    CryptLibPath += "cryptlib_md_d.lib";
                    Log4LibPath += "log4cplusUD.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetworkD.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_64D.lib";
                    }
                }
            }
            else
            {
                if (UE3BuildConfiguration.bVS2013)
                {
                    MathLibPath += "Math_2013_Client.lib";
                    CoreLibPath += "Core_2013_Client.lib";
                    SharedLibPath += "Shared_2013_Client.lib";
                    CryptLibPath += "cryptlib_2013_md.lib";
                    Log4LibPath += "log4cplusU.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork_2013.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_64.lib";
                    }
                }
                else
                {
                    MathLibPath += "Math_Client.lib";
                    CoreLibPath += "Core_Client.lib";
                    SharedLibPath += "Shared_Client.lib";
                    CryptLibPath += "cryptlib_md.lib";
                    Log4LibPath += "log4cplusU.lib";

                    if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32)
                    {
                        NetworkLibPath += "ClientNetwork.lib";
                    }
                    else if (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64)
                    {
                        NetworkLibPath += "ClientNetwork_64.lib";
                    }
                }
            }

            FinalLinkEnvironment.AdditionalLibraries.Add(MathLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(CoreLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(SharedLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(NetworkLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(CryptLibPath);
            FinalLinkEnvironment.AdditionalLibraries.Add(Log4LibPath);

            if (UE3BuildConfiguration.bBuildEditor &&
                (GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win32 || GameCPPEnvironment.TargetPlatform == CPPTargetPlatform.Win64))
            {
                GameProjects.Add(new UE3ProjectDesc("MU2Editor/MU2Editor.vcxproj"));
                GameCPPEnvironment.IncludePaths.Add("MU2Editor/Inc");
            }

            GameCPPEnvironment.Definitions.Add("GAMENAME=MU2GAME");
        }
    }
}

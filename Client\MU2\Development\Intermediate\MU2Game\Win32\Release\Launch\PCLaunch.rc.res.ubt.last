C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x86\rc.exe
 /nologo /l 0x409 /i "Core/Inc/Epic" /i "Core/Inc/Licensee" /i "Core/Inc" /i "Engine/Inc" /i "GameFramework/Inc" /i "IpDrv/Inc" /i "UnrealEd/Inc" /i "UnrealEd/FaceFX" /i "UnrealEdCLR/Inc" /i "UnrealSwarm/Inc" /i "GwNavEngine/Inc" /i "GwNavEditor/Inc" /i "../External/gwnav/3rd/tbb41_20121003oss/include" /i "../External/tinyXML" /i "../External/SpeedTree/Include" /i "GFxUI/Inc" /i "WinDrv/Inc" /i "D3D9Drv/Inc" /i "D3D11Drv/Inc" /i "XAudio2/Inc" /i "Launch/Inc" /i "../Tools/UnrealLightmass/Public" /i "$(CommonProgramFiles)" /i "OnlineSubsystemSteamworks/Inc" /i "../External/wxWidgets/include" /i "../External/wxWidgets/lib/vc_dll" /i "../External/wxExtended/wxDockit/include" /i "../External/Simplygon/Inc" /i "GFxUIEditor/Inc" /i "MU2Game/Inc" /i "../../../../" /i "../../../../Shared" /i "../../../../Shared/Scripts" /i "../../../../server/Development" /i "../../../../Vendor" /i "../../../../Vendor/boost" /i "../../../../Vendor/jsoncpp/include" /i "../../../../Vendor/log4cplus/include" /i "MU2Editor/Inc" /i "Launch\Src" /d "UNICODE" /d "_UNICODE" /d "__UNREAL__" /d "WITH_GWNAV" /d "KY_BUILD_RELEASE" /d "WITH_FACEFX=0" /d "USE_BINK_CODEC=0" /d "WITH_SUBSTANCE_AIR=0" /d "WITH_GFx=1" /d "WITH_GFx_AUDIO=1" /d "WITH_GFx_VIDEO=1" /d "WITH_GFx_IME=1" /d "WITH_RECAST=1" /d "WITH_STEAMWORKS=1" /d "WITH_GAMECENTER=0" /d "WITH_PANORAMA=0" /d "WITH_UE3_NETWORKING=1" /d "UE3_LEAN_AND_MEAN=0" /d "WITH_EDITOR=1" /d "WITH_PERFORCE=1" /d "PX_NO_WARNING_PRAGMAS" /d "USE_SCOPED_MEM_STATS=1" /d "WJC_VS2022=1" /d "WJC_VS2015=0" /d "WJC_VS2013=0" /d "WJC_FBX2015=1" /d "WINDOWS_IGNORE_PACKING_MISMATCH=1" /d "_SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING=1" /d "_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS=1" /d "_WINDOWS=1" /d "WIN32=1" /d "_WIN32_WINNT=0x0502" /d "WINVER=0x0502" /d "WITH_STEAMWORKS=1" /d "DEDICATED_SERVER=0" /d "PNG_NO_FLOATING_POINT_SUPPORTED=1" /d "WITH_JPEG=1" /d "WXUSINGDLL" /d "wxUSE_NO_MANIFEST" /d "WITH_FBX=1" /d "FBXSDK_NEW_API" /d "USE_DYNAMIC_ES2_RHI=0" /d "WITH_KISSFFT=1" /d "XDKINSTALLED=0" /d "WITH_SPEEDTREE=0" /d "DWTRIOVIZSDK=0" /d "WITH_SIMPLYGON_DLL=1" /d "WITH_SIMPLYGON=1" /d "WITH_OPEN_AUTOMATE=1" /d "WITH_MANAGED_CODE=1" /d "NDEBUG=1" /d "FINAL_RELEASE=0" /d "MU2_RELEASE" /d "GAMENAME=MU2GAME" /fo "F:\Release_Branch\Client\MU2\Development\Intermediate\MU2Game\Win32\Release\Launch\PCLaunch.rc.res" "F:\Release_Branch\Client\MU2\Development\Src\Launch\Resources\PCLaunch.rc"
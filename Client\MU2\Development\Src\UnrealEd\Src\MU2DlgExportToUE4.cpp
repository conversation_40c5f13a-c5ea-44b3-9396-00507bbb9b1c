
#include "UnFbxExporter.h"
#include "UnrealEd.h"
#include "EngineAnimClasses.h"
#include "MU2DlgExportToUE4.h"
#include "ScopedTransaction.h"
#include "BusyCursor.h"
#include "LevelUtils.h"
#include "UnObjectTools.h"
#include "EngineSoundClasses.h"
#include "EnginePrefabClasses.h"
#include "EngineFoliageClasses.h"
#include "EngineFogVolumeClasses.h"
#include "FileHelpers.h"

TMap<const FString, const FString> MaterialExpressionNameIndirectMap;
#define REDIRECT_EXPRESSION_NAME(ExpName) \
{	\
	FString IndirectName = #ExpName;	\
	MaterialExpressionNameIndirectMap.Set(IndirectName, IndirectName + TEXT("_DEPRECATED"));	\
}

const FString* FindRedirectExpressionClassName(const FString& InClassName)
{
	if (MaterialExpressionNameIndirectMap.Num() == 0)
	{
		REDIRECT_EXPRESSION_NAME(MaterialExpressionActorWorldPosition);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionCameraWorldPosition);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionConstantClamp);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionCustomTexture);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionDepthBiasedAlpha);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionDepthBiasedBlend);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionDestColor);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionDestDepth);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionFluidNormal);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionFoliageImpulseDirection);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionFoliageNormalizedRotationAxisAndAngle);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionLensFlareIntensity);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionLensFlareOcclusion);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionLensFlareRadialDistance);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionLensFlareRayDistance);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionLensFlareSourceDistance);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionMeshEmitterVertexColor);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionObjectWorldPosition);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionOcclusionPercentage);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionReflectionVector);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionScreenSize);
		//REDIRECT_EXPRESSION_NAME(MaterialExpressionTerrainLayerCoords);
		//REDIRECT_EXPRESSION_NAME(MaterialExpressionTerrainLayerSwitch);
		//REDIRECT_EXPRESSION_NAME(MaterialExpressionTerrainLayerWeight);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionTexelSize);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionWindDirectionAndSpeed);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionMeshEmitterDynamicParameter);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionRotateAboutAxis);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionCameraVector);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionConstant3Vector);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionConstant4Vector);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionDesaturation);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionWorldNormal);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionScreenPosition);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionTextureSampleParameterMeshSubUV);
		REDIRECT_EXPRESSION_NAME(MaterialExpressionTextureSampleParameterNormal);
	}

	return MaterialExpressionNameIndirectMap.Find(InClassName);
}

BEGIN_EVENT_TABLE(WxDlgExportToUE4, wxDialog)
	EVT_BUTTON(SELECT_SAVE_FOLDER, WxDlgExportToUE4::_wxFB_OnSaveFolderClick)
	EVT_BUTTON(SAVE_ALL_ASSET_INDIVIDUAL, WxDlgExportToUE4::_wxFB_OnSaveAllAssetIndividualClick)
	EVT_BUTTON(SAVE_ALL_ASSET, WxDlgExportToUE4::_wxFB_OnSaveAllAssetClick)
	EVT_BUTTON(SAVE_LEVEL_ASSET, WxDlgExportToUE4::_wxFB_OnSaveLevelAssetClick)
END_EVENT_TABLE()

WxDlgExportToUE4::WxDlgExportToUE4(wxWindow* parent, wxWindowID id, const wxString& title, const wxPoint& pos, const wxSize& size, long style) : wxDialog(parent, id, title, pos, size, style)
{
	this->SetSizeHints( wxSize( -1,-1 ), wxDefaultSize );
	
	wxBoxSizer* SizerParent;
	SizerParent = new wxBoxSizer( wxVERTICAL );
	
	wxBoxSizer* SizerSaveFolder;
	SizerSaveFolder = new wxBoxSizer( wxHORIZONTAL );
	
	BtnSaveFolder = new wxButton( this, SELECT_SAVE_FOLDER, wxT("저장폴더선택"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerSaveFolder->Add( BtnSaveFolder, 0, wxALL, 5 );
	
	TextSaveFolder = new wxStaticText( this, wxID_ANY, wxT(".\\Content"), wxDefaultPosition, wxSize( 700,-1 ), 0 );
	TextSaveFolder->Wrap( -1 );
	SizerSaveFolder->Add( TextSaveFolder, 1, wxALL, 5 );
	
	
	SizerParent->Add( SizerSaveFolder, 0, wxEXPAND, 5 );
	
	StaticLine1 = new wxStaticLine( this, wxID_ANY, wxDefaultPosition, wxDefaultSize, wxLI_HORIZONTAL );
	SizerParent->Add( StaticLine1, 0, wxALL|wxEXPAND, 5 );
	
	wxBoxSizer* SizerSaveAsset;
	SizerSaveAsset = new wxBoxSizer( wxHORIZONTAL );
	
	TextSaveAssets = new wxStaticText( this, wxID_ANY, wxT("패키지 저장"), wxDefaultPosition, wxDefaultSize, 0 );
	TextSaveAssets->Wrap( -1 );
	TextSaveAssets->SetForegroundColour( wxSystemSettings::GetColour( wxSYS_COLOUR_WINDOWTEXT ) );
	
	SizerSaveAsset->Add( TextSaveAssets, 0, wxALL, 5 );
	
	
	SizerParent->Add( SizerSaveAsset, 0, wxEXPAND, 5 );
	
	wxBoxSizer* SizerPackage;
	SizerPackage = new wxBoxSizer( wxHORIZONTAL );
	
	wxBoxSizer* SizerCheckBoxPackage;
	SizerCheckBoxPackage = new wxBoxSizer( wxHORIZONTAL );
	
	CheckBoxTexture = new wxCheckBox( this, wxID_ANY, wxT("텍스쳐"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxTexture, 0, wxALL, 5 );
	
	CheckBoxMaterialFunction = new wxCheckBox( this, wxID_ANY, wxT("머티리얼 함수"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxMaterialFunction, 0, wxALL, 5 );
	
	CheckBoxMaterial = new wxCheckBox( this, wxID_ANY, wxT("머티리얼"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxMaterial, 0, wxALL, 5 );
	
	CheckBoxMaterialInstance = new wxCheckBox( this, wxID_ANY, wxT("머티리얼 인스턴스"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxMaterialInstance, 0, wxALL, 5 );
	
	CheckBoxStaticMesh = new wxCheckBox( this, wxID_ANY, wxT("스태틱 메시"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxStaticMesh, 0, wxALL, 5 );
	
	CheckBoxSkeletalMesh = new wxCheckBox( this, wxID_ANY, wxT("스켈레탈 메시"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxSkeletalMesh, 0, wxALL, 5 );
	
	CheckBoxAnim = new wxCheckBox( this, wxID_ANY, wxT("애니메이션"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxAnim, 0, wxALL, 5 );
	
	CheckBoxSoundNode = new wxCheckBox( this, wxID_ANY, wxT("사운드 노드(WAV)"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxSoundNode, 0, wxALL, 5 );
	
	CheckBoxSoundCue = new wxCheckBox( this, wxID_ANY, wxT("사운드 큐"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxSoundCue, 0, wxALL, 5 );
	
	CheckBoxParticle = new wxCheckBox( this, wxID_ANY, wxT("파티클"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerCheckBoxPackage->Add( CheckBoxParticle, 0, wxALL, 5 );
	
	CheckBoxPhysicsAsset = new wxCheckBox( this, wxID_ANY, wxT("피직애셋"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxPhysicsAsset->Enable( false );
	CheckBoxPhysicsAsset->Hide();
	
	SizerCheckBoxPackage->Add( CheckBoxPhysicsAsset, 0, wxALL, 5 );
	
	
	SizerPackage->Add( SizerCheckBoxPackage, 1, wxEXPAND, 5 );
	
	wxBoxSizer* SizerPackageButton;
	SizerPackageButton = new wxBoxSizer( wxHORIZONTAL );
	
	BtnAllSaveIndividual = new wxButton( this, SAVE_ALL_ASSET_INDIVIDUAL, wxT("개별로 저장"), wxDefaultPosition, wxDefaultSize, 0 );
	BtnAllSaveIndividual->Enable( false );
	BtnAllSaveIndividual->Hide();
	
	SizerPackageButton->Add( BtnAllSaveIndividual, 0, wxALL, 5 );
	
	BtnAllSave = new wxButton( this, SAVE_ALL_ASSET, wxT("저장"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerPackageButton->Add( BtnAllSave, 0, wxALIGN_BOTTOM|wxALIGN_RIGHT|wxALL, 5 );
	
	
	SizerPackage->Add( SizerPackageButton, 0, wxEXPAND, 5 );
	
	
	SizerParent->Add( SizerPackage, 0, wxEXPAND, 5 );
	
	m_staticline7 = new wxStaticLine( this, wxID_ANY, wxDefaultPosition, wxDefaultSize, wxLI_HORIZONTAL );
	SizerParent->Add( m_staticline7, 0, wxEXPAND | wxALL, 5 );
	
	wxBoxSizer* SizerSaveMapAsset;
	SizerSaveMapAsset = new wxBoxSizer( wxHORIZONTAL );
	
	TextSaveMapAssets = new wxStaticText( this, wxID_ANY, wxT("맵 저장"), wxDefaultPosition, wxDefaultSize, 0 );
	TextSaveMapAssets->Wrap( -1 );
	TextSaveMapAssets->SetForegroundColour( wxSystemSettings::GetColour( wxSYS_COLOUR_WINDOWTEXT ) );
	
	SizerSaveMapAsset->Add( TextSaveMapAssets, 0, wxALL, 5 );
	
	
	SizerParent->Add( SizerSaveMapAsset, 0, wxEXPAND, 5 );
	
	wxBoxSizer* SizerMap;
	SizerMap = new wxBoxSizer( wxHORIZONTAL );
	
	wxBoxSizer* SizerMapCheckBox;
	SizerMapCheckBox = new wxBoxSizer( wxHORIZONTAL );
	
	CheckBoxLandScape = new wxCheckBox( this, wxID_ANY, wxT("랜드 스케이프"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxLandScape->SetValue(true); 
	SizerMapCheckBox->Add( CheckBoxLandScape, 0, wxALL, 5 );
	
	CheckBoxLight = new wxCheckBox( this, wxID_ANY, wxT("라이트"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxLight->SetValue(true); 
	SizerMapCheckBox->Add( CheckBoxLight, 0, wxALL, 5 );
	
	CheckBoxSkelMeshActor = new wxCheckBox( this, wxID_ANY, wxT("스켈레탈 메시 액터"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxSkelMeshActor->SetValue(true); 
	SizerMapCheckBox->Add( CheckBoxSkelMeshActor, 0, wxALL, 5 );
	
	CheckBoxStaticMeshActor = new wxCheckBox( this, wxID_ANY, wxT("스태틱 메시 액터"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxStaticMeshActor->SetValue(true); 
	SizerMapCheckBox->Add( CheckBoxStaticMeshActor, 0, wxALL, 5 );
	
	CheckBoxSoundActor = new wxCheckBox( this, wxID_ANY, wxT("사운드 액터"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxSoundActor->SetValue(true); 
	SizerMapCheckBox->Add( CheckBoxSoundActor, 0, wxALL, 5 );
	
	
	SizerMap->Add( SizerMapCheckBox, 1, wxEXPAND, 5 );
	
	wxBoxSizer* SizerMapSaveBtn;
	SizerMapSaveBtn = new wxBoxSizer( wxVERTICAL );
	
	BtnLevelSave = new wxButton( this, SAVE_LEVEL_ASSET, wxT("저장"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerMapSaveBtn->Add( BtnLevelSave, 0, wxALIGN_RIGHT|wxALL|wxALIGN_BOTTOM, 5 );
	
	
	SizerMap->Add( SizerMapSaveBtn, 1, wxEXPAND, 5 );
	
	
	SizerParent->Add( SizerMap, 0, wxEXPAND, 5 );
	
	StaticLine2 = new wxStaticLine( this, wxID_ANY, wxDefaultPosition, wxDefaultSize, wxLI_HORIZONTAL );
	SizerParent->Add( StaticLine2, 0, wxEXPAND | wxALL, 5 );
	
	wxBoxSizer* SizerSaveOption;
	SizerSaveOption = new wxBoxSizer( wxVERTICAL );
	
	TextSaveOption = new wxStaticText( this, wxID_ANY, wxT("저장 옵션"), wxDefaultPosition, wxDefaultSize, 0 );
	TextSaveOption->Wrap( -1 );
	SizerSaveOption->Add( TextSaveOption, 0, wxALIGN_BOTTOM|wxALL, 5 );
	
	
	SizerParent->Add( SizerSaveOption, 0, wxEXPAND, 5 );
	
	wxBoxSizer* SizerOption;
	SizerOption = new wxBoxSizer( wxHORIZONTAL );
	
	CheckBoxOverWrite = new wxCheckBox( this, wxID_ANY, wxT("기존파일 덮어쓰기"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerOption->Add( CheckBoxOverWrite, 0, wxALL, 5 );
	
	CheckBoxOptionAnimMeshExport = new wxCheckBox( this, wxID_ANY, wxT("애니메이션 스켈메시 같이 익스포트"), wxDefaultPosition, wxDefaultSize, 0 );
	CheckBoxOptionAnimMeshExport->SetValue(true); 
	SizerOption->Add( CheckBoxOptionAnimMeshExport, 0, wxALL, 5 );
	
	CheckBoxSaveMetaOnly = new wxCheckBox( this, wxID_ANY, wxT("메타데이터만 세이브"), wxDefaultPosition, wxDefaultSize, 0 );
	SizerOption->Add( CheckBoxSaveMetaOnly, 0, wxALL, 5 );
	
	
	SizerParent->Add( SizerOption, 0, wxEXPAND, 5 );
	
	StaticLine3 = new wxStaticLine( this, wxID_ANY, wxDefaultPosition, wxDefaultSize, wxLI_HORIZONTAL );
	SizerParent->Add( StaticLine3, 0, wxEXPAND | wxALL, 5 );
	
	wxBoxSizer* SizerSaveBtn;
	SizerSaveBtn = new wxBoxSizer( wxHORIZONTAL );
	
	TextExportedPackage = new wxStaticText( this, wxID_ANY, wxEmptyString, wxDefaultPosition, wxSize( 700,-1 ), 0 );
	TextExportedPackage->Wrap( -1 );
	SizerSaveBtn->Add( TextExportedPackage, 1, wxALL|wxALIGN_BOTTOM, 5 );
	
	
	SizerParent->Add( SizerSaveBtn, 1, wxEXPAND, 5 );
	
	
	this->SetSizer( SizerParent );
	this->Layout();
	
	this->Centre( wxBOTH );






















	TextSaveFolder->SetLabel(*GApp->LastDir[LD_GENERIC_EXPORT]);
	SetSupportClassList();
}

WxDlgExportToUE4::~WxDlgExportToUE4()
{
}
//---------------------------------------------------------------------------------------

struct FDeferredCommandOnTickInfo
{
	FLOAT WaitTime;
	FExportMapCallbackEventDevice* CallbackEventDevice;

	FDeferredCommandOnTickInfo(FExportMapCallbackEventDevice* InCallbackDev, FLOAT InWaitTime)
		: CallbackEventDevice(InCallbackDev), WaitTime(InWaitTime)
	{
	}
};

TArray<FDeferredCommandOnTickInfo> DeferredCommandsOnTick;
void TickForExportUE4(FLOAT DeltaSeconds)
{
	if (DeferredCommandsOnTick.Num())
	{
		DeferredCommandsOnTick(0).WaitTime -= DeltaSeconds;

		if (DeferredCommandsOnTick(0).WaitTime < 0.0f)
		{
			FExportMapCallbackEventDevice* CallbackEventDevice = DeferredCommandsOnTick(0).CallbackEventDevice;

			DeferredCommandsOnTick.Remove(0);
			CallbackEventDevice->ExportAndProcessNext();
		}
	}
}

// 4에서 이렇게 이름이 정형화 되지 않으면 인스펙트에서 두개의 컴포넌트가 나온다.
void FExportMapCallbackEventDevice::RenameMapActorComponent(const UWorld* InWorld)
{
	for (FObjectIterator It; It; ++It)
	{
		UObject* Obj = *It;

		if (!Obj->IsIn(InWorld))
			continue;

		AStaticMeshActor* StaticMeshActor = Cast<AStaticMeshActor>(Obj);
		if (StaticMeshActor && StaticMeshActor->StaticMeshComponent)
		{
			StaticMeshActor->StaticMeshComponent->Rename(TEXT("StaticMeshComponent0"));
			continue;
		}

		ASkeletalMeshActor* SkelMeshActor = Cast<ASkeletalMeshActor>(Obj);
		if (SkelMeshActor && SkelMeshActor->SkeletalMeshComponent)
		{
			SkelMeshActor->SkeletalMeshComponent->Rename(TEXT("SkeletalMeshComponent0"));
			continue;
		}

		AEmitter* Emitter = Cast<AEmitter>(Obj);
		if (Emitter && Emitter->ParticleSystemComponent)
		{
			Emitter->ParticleSystemComponent->Rename(TEXT("ParticleSystemComponent0"));
			continue;
		}

		ALight* Light = Cast<ALight>(Obj);
		if (Light && Light->LightComponent)
		{
			Light->LightComponent->Rename(TEXT("LightComponent0"));
			continue;
		}

		AInterpActor* InterpActor = Cast<AInterpActor>(Obj);
		if (InterpActor && InterpActor->StaticMeshComponent)
		{
			InterpActor->StaticMeshComponent->Rename(TEXT("StaticMeshComponent0"));
			continue;
		}
	}
}

void FExportMapCallbackEventDevice::Send( ECallbackEventType InType, DWORD InFlag )
{
	if (InFlag != MapChangeEventFlags::NewMap)
		return;

	GCallbackEvent->Unregister(CALLBACK_MapChange, this);

	// 랜드 스케이프가 있으면 "UpdateLandscapeEditorData" 커맨드가 끝나고 CALLBACK_WorldChange 가 호출될 때 익스포트 한다. 아니면 바로 한다.
	for (FActorIterator It; It; ++It)
	{
		ALandscape* Landscape = Cast<ALandscape>(*It);
		if (Landscape)
		{
			GCallbackEvent->Register(CALLBACK_WorldChange, this);
			return;
		}
	}

	DeferredCommandsOnTick.AddItem(FDeferredCommandOnTickInfo(this, 1.0f));
}

void FExportMapCallbackEventDevice::ExportAndProcessNext()
{
	StaticText->SetLabel(*ExportFilename);
	const FScopedBusyCursor BusyCursor;

	GExportForUE4 = TRUE;
	GExportUntypedBulkDataForUE4 = TRUE;
	ObjectTools::GForceExportGroupsAsSubDirs = TRUE;

	// void ALandscapeProxy::PostLoad()
	// GEngine->DeferredCommands.AddUniqueItem(TEXT("UpdateLandscapeEditorData"));
	// -> 여기서 틱을 한번 돌고 엔진의 Exec 에서 렌드스케이프의 LayerInfoMap 세팅을 한다. 그래서 한번 틱을 돌고 익스포트 한다.

	// 4에 맞게 컴포넌트 이름을 다시 정한다.
	RenameMapActorComponent(GWorld);

	ULevelExporterT3D* ExporterToUse = ConstructObject<ULevelExporterT3D>(ULevelExporterT3D::StaticClass());

	UExporter::FExportToFileParams Params;
	Params.Object = GWorld;
	Params.Exporter = ExporterToUse;
	Params.Filename = *ExportFilename;
	Params.InSelectedOnly = FALSE;
	Params.NoReplaceIdentical = FALSE;
	Params.Prompt = FALSE;
	Params.bUseFileArchive = FALSE;
	Params.WriteEmptyFiles = FALSE;

	UExporter::ExportToFileEx(Params);

	// 하이트맵 raw 파일로 뽑았는데 맵 패키지 안에 가중치 텍스쳐 등의 정보도 있어서 ULevelExporterT3D 에서 익스포트 한다.
	//ExportLandscapeHeightmap();

	if (Next)
	{
		Next->LoadMapForExport();
	}

	GExportForUE4 = FALSE;
	GExportUntypedBulkDataForUE4 = FALSE;
	ObjectTools::GForceExportGroupsAsSubDirs = FALSE;

	delete this;
}

void FExportMapCallbackEventDevice::Send(ECallbackEventType InType)
{
	if (CALLBACK_WorldChange)
	{
		GCallbackEvent->Unregister(CALLBACK_WorldChange, this);
		DeferredCommandsOnTick.AddItem(FDeferredCommandOnTickInfo(this, 1.0f));
	}
}

void FExportMapCallbackEventDevice::ExportLandscapeHeightmap()
{
	TArray<ALandscape*> LandcapeList;

	for (FActorIterator It; It; ++It)
	{
		ALandscape* Landscape = Cast<ALandscape>(*It);
		if (Landscape)
			LandcapeList.AddItem(Landscape);
	}

	if (LandcapeList.Num())
	{
		ALandscape* Landscape = LandcapeList(0);

		ULandscapeInfo* Info = Landscape->GetLandscapeInfo(FALSE);
		if (Info)
		{
			FFilename LandscapeFilename = ExportFilename;
			
			//FString BaseFileName = FString::Printf(TEXT("%s%d"), *LandscapeFilename.GetBaseFilename(), i);
			FString BaseFileName = *LandscapeFilename.GetBaseFilename();

			LandscapeFilename = LandscapeFilename.GetPath() + TEXT("\\") + BaseFileName;
			FString HeightFieldFilename = LandscapeFilename + TEXT(".raw");

			TArray<FName> Layernames;
			TArray<FString> Filenames;

			Filenames.AddItem(HeightFieldFilename);

			Info->Export(Layernames, Filenames);

			// 랜드스케이프 정보
			FString infoFilename = LandscapeFilename + TEXT(".LSINFO");

			FOutputDevice* TextBuffer = new FStringOutputDevice();

			FStringOutputDevice& StringBuffer = *((FStringOutputDevice*)TextBuffer);

			StringBuffer.Logf(TEXT("LOCATION_X=%6f\n"), Landscape->Location.X);
			StringBuffer.Logf(TEXT("LOCATION_Y=%6f\n"), Landscape->Location.Y);
			StringBuffer.Logf(TEXT("LOCATION_Z=%6f\n"), Landscape->Location.Z);
			StringBuffer.Logf(TEXT("YAW=%d\n"), Landscape->Rotation.Yaw);
			StringBuffer.Logf(TEXT("PITCH=%d\n"), Landscape->Rotation.Pitch);
			StringBuffer.Logf(TEXT("ROLL=%d\n"), Landscape->Rotation.Roll);

			FVector Scale = Landscape->DrawScale3D * Landscape->DrawScale;
			StringBuffer.Logf(TEXT("SCALE_X=%6f\n"), Scale.X);
			StringBuffer.Logf(TEXT("SCALE_Y=%6f\n"), Scale.Y);
			StringBuffer.Logf(TEXT("SCALE_Z=%6f\n"), Scale.Z);

			if (Landscape->LandscapeMaterial)
				StringBuffer.Logf(TEXT("MATERIAL=%s\n"), *Landscape->LandscapeMaterial->GetPathName());

			appSaveStringToFile(StringBuffer, *infoFilename);

			delete TextBuffer;
		}
	}
}

void FExportMapCallbackEventDevice::LoadMapForExport()
{
	GCallbackEvent->Register(CALLBACK_MapChange, this);
	FEditorFileUtils::LoadMap(LevelFilename);
}

UBOOL WxDlgExportToUE4::CheckOverWriteOption(const FString& InFilename)
{
	if (CheckBoxOverWrite->IsChecked() == false)
	{
		if (GFileManager->FileSize(*InFilename) != INDEX_NONE)
			return FALSE;
	}

	return TRUE;
}

FString WxDlgExportToUE4::MakeSaveFilename(UObject* InObj, const FString& InPath, const FString& InExt, UBOOL bUseProvidedExportPath)
{
	if (bUseProvidedExportPath)
	{
		return InPath + PATH_SEPARATOR + InObj->GetName() + InExt;
	}

	// GetPathName 에서 언리얼4의 파일포맷 (pakagename.pakagename) 으로 변환하지 않기위해
	UBOOL OldExportForUE4 = GExportForUE4;
	GExportForUE4 = FALSE;

	// InPath 제일 끝에 "\\" 가 붙지 않고 넘어온다. 디렉토리 체크위하여
	FString Filename = InPath;

	if (1)
	{
		// 패키지 안의 "." 을 폴더로 만든다.
		// 그럴리는 없지만 최종 아웃터가 자기자신이면 None 리턴
		FString ObjPathInPkg = InObj->GetPathName();

		TArray<FString> ObjPathList;
		ObjPathInPkg.ParseIntoArray(&ObjPathList, TEXT("."), TRUE);

		if (ObjPathInPkg != TEXT("None"))
		{
			// 마지막은 자신 이름이니까 붙이지 않는다.
			INT MaxLoop = ObjPathList.Num() - 1;
			for (INT i = 0; i < MaxLoop; ++i)
			{
				Filename = Filename + PATH_SEPARATOR + ObjPathList(i);
			}
		}
	}
	else
	{
		ObjectTools::GetDirectoryFromObjectPath(InObj, Filename);
	}

	// 디렉토리가 없으면 만들어서 저장하는게 아니라 저장을 못한다.
	GFileManager->MakeDirectory(*Filename, TRUE);

	UAnimSequence* animSeq = Cast<UAnimSequence>(InObj);

	// 애니메이션 시퀀스는 오브젝트 이름이 아닌 시퀀스 이름으로 만든다.
	FString objName = animSeq ? animSeq->SequenceName.ToString() : InObj->GetName();

	GExportForUE4 = OldExportForUE4;

	// 최종 저장 패스를 만든다.
	return Filename + PATH_SEPARATOR + objName + InExt;
}

UBOOL WxDlgExportToUE4::ExportObjectMetaDataInternal(UObject* InObj, UClass* InExporterClass, const FString& InPath, const FString InExt, UBOOL bUseProvidedExportPath)
{
	FString Filename = MakeSaveFilename(InObj, InPath, InExt, bUseProvidedExportPath);

	if (!CheckOverWriteOption(Filename))
		return FALSE;

	UExporter* ExporterToUse = FindExporter(InExporterClass);

	UExporter::FExportToFileParams Params;
	Params.Object = InObj;
	Params.Exporter = ExporterToUse;
	Params.Filename = *Filename;
	Params.InSelectedOnly = FALSE;
	Params.NoReplaceIdentical = FALSE;
	Params.Prompt = FALSE;
	Params.bUseFileArchive = FALSE;
	Params.WriteEmptyFiles = FALSE;
	return UExporter::ExportToFileEx(Params) == 1;
}

void WxDlgExportToUE4::ExportAnimSequence(UObject* InObj, const FString& InPath, UBOOL bUseProvidedExportPath)
{
	UAnimSequence* animSeq = Cast<UAnimSequence>(InObj);
	if (animSeq == nullptr)
		return;

	if (Cast<UAnimSet>(animSeq->GetOuter()) == nullptr)
		return;

	UAnimSet* animSet = animSeq->GetAnimSet();
	if (animSet == nullptr)
		return;

	USkeletalMesh* SkelMesh = LoadObject<USkeletalMesh>(NULL, *animSet->PreviewSkelMeshName.ToString(), NULL, LOAD_None, NULL);
	if (SkelMesh == nullptr)
		return;

	// 메타파일 저장
	if (ExportObjectMetaDataInternal(InObj, UObjectExporterT3D::StaticClass(), InPath, TEXT(".T3DANIM"), bUseProvidedExportPath))
	{
		BOOL bSaveSkeletalMesh = FALSE;// CheckBoxOptionAnimMeshExport->IsChecked() ? TRUE : FALSE;

		FString Filename = MakeSaveFilename(InObj, InPath, TEXT(""), bUseProvidedExportPath);

		UnFbx::CFbxExporter* Exporter = UnFbx::CFbxExporter::GetInstance();

		Exporter->CreateDocument();
		Exporter->ExportAnimSequence(animSeq, SkelMesh, bSaveSkeletalMesh);
		Exporter->WriteToFile(*Filename);
	}
}

FString WxDlgExportToUE4::GetPathAfterContent(const FString& InSrcFilename)
{
	FFilename tempSrcFilename = InSrcFilename;
	FString srcPath = tempSrcFilename.GetPath();

	FString beforeContentStr, afterContentStr = TEXT("");

	srcPath.Split(TEXT("Content\\"), &beforeContentStr, &afterContentStr);

	return afterContentStr;
}
//-------------------------------------------------------------------------------------------

void WxDlgExportToUE4::SetSupportClassList()
{
	SupportClassInfoList.AddItem(FSupportClassInfo(UAnimSequence::StaticClass(), UObjectExporterT3D::StaticClass(), TEXT(".T3DANIM"), TRUE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UStaticMesh::StaticClass(),UStaticMeshExporterT3D::StaticClass(), TEXT(".T3DSTM"), TRUE));
	SupportClassInfoList.AddItem(FSupportClassInfo(USkeletalMesh::StaticClass(), UObjectExporterT3D::StaticClass(), TEXT(".T3DSKM"), TRUE));
	SupportClassInfoList.AddItem(FSupportClassInfo(USoundCue::StaticClass(), UObjectExporterT3D::StaticClass(), TEXT(".T3DSNDCUE"), FALSE));
	SupportClassInfoList.AddItem(FSupportClassInfo(USoundNodeWave::StaticClass(), UObjectExporterT3D::StaticClass(), TEXT(".T3DWAVE"), TRUE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UMaterialFunction::StaticClass(), UMaterialFunctionExporterT3D::StaticClass(), TEXT(".T3DMATFUNC"), FALSE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UMaterial::StaticClass(), UMaterialExporterT3D::StaticClass(), TEXT(".T3DMAT"), FALSE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UMaterialInstance::StaticClass(), UObjectExporterT3D::StaticClass(), TEXT(".T3DMATINS"), FALSE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UTexture::StaticClass(), UTextureExporterT3D::StaticClass(), TEXT(".T3DT2D"), TRUE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UTextureCube::StaticClass(), UTextureExporterT3D::StaticClass(), TEXT(".T3DT2D"), FALSE));
	SupportClassInfoList.AddItem(FSupportClassInfo(UParticleSystem::StaticClass(), UObjectExporterT3D::StaticClass(), TEXT(".T3DPTS"), FALSE));
}

WxDlgExportToUE4::FSupportClassInfo* WxDlgExportToUE4::GetSupportClassInfo(UObject* InObj)
{
	for (INT i = 0; i < SupportClassInfoList.Num(); ++i)
	{
		const UClass* SupportClass = SupportClassInfoList(i).SupportClass;
		if (InObj->IsA(SupportClass))
		{
			return &SupportClassInfoList(i);
		}
	}

	return nullptr;
}

UBOOL WxDlgExportToUE4::CheckExportOption(UObject* InObj)
{
	if (!GetSupportClassInfo(InObj))
		return FALSE;

	if (InObj->IsA(UTexture::StaticClass()) && CheckBoxTexture->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(UStaticMesh::StaticClass()) && CheckBoxStaticMesh->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(USkeletalMesh::StaticClass()) && CheckBoxSkeletalMesh->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(UMaterialFunction::StaticClass()) && CheckBoxMaterialFunction->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(UMaterial::StaticClass()))
	{
		if (CheckBoxMaterial->IsChecked() == false)
			return FALSE;

		if (Cast<UPreviewMaterial>(InObj))
			return FALSE;
	}

	if (InObj->IsA(UMaterialInstance::StaticClass()) && CheckBoxMaterialInstance->IsChecked() == false)
		return FALSE;
	
	if (InObj->IsA(USoundCue::StaticClass())  && CheckBoxSoundCue->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(USoundNodeWave::StaticClass()) && CheckBoxSoundNode->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(UAnimSequence::StaticClass()) && CheckBoxAnim->IsChecked() == false)
		return FALSE;

	if (InObj->IsA(UParticleSystem::StaticClass()) && CheckBoxParticle->IsChecked() == false)
		return FALSE;

	//if (InObj->IsA(UPhysicsAsset::StaticClass()) && CheckBoxPhysicsAsset->IsChecked() == false)
	//	return FALSE;

	return TRUE;
}

void WxDlgExportToUE4::OnSaveAllAssetIndividualClick(wxCommandEvent& event)
{

}

void WxDlgExportToUE4::OnSaveFolderClick(wxCommandEvent& event)
{
	wxDirDialog dlg(GApp->EditorFrame, *LocalizeUnrealEd("Export"), *GApp->LastDir[LD_GENERIC_EXPORT]);
	if (dlg.ShowModal() != wxID_OK)
	{
		return;
	}

	TextSaveFolder->SetLabel(dlg.GetPath());

	GApp->LastDir[LD_GENERIC_EXPORT] = dlg.GetPath();
}

UExporter* WxDlgExportToUE4::FindExporter(UClass* InExporterClass)
{
	for (INT i = 0; i < ObjectTools::GAssembleListOfExporters.Num(); ++i)
	{
		if (ObjectTools::GAssembleListOfExporters(i)->IsA(InExporterClass))
		{
			return ObjectTools::GAssembleListOfExporters(i);
		}
	}
	return nullptr;
}

void WxDlgExportToUE4::ConditionalAddForExport(UObject* InObj, TArray<UObject*>& InObjectList)
{
	if (CheckBoxSaveMetaOnly->IsChecked() == false)
	{
		InObjectList.AddItem(InObj);
	}
}

void WxDlgExportToUE4::MakeUniqueNameInParticleModule(UObject* InObj)
{
	// 파티클 모듈의 컴포넌트 이름이 같아서 Ambiguous search 워닝이 나온다. 아웃터가 다른데도 나온다.
	if (InObj && InObj->IsA(UParticleSystem::StaticClass()))
	{
		for (FObjectIterator It; It; ++It)
		{
			UObject* InnerObj = *It;
			if (!InnerObj->IsIn(InObj))
				continue;

			// 4에선 파티클 모듈의 아웃터가 파티클 시스템이다. 3에선 LODLevel 이다. 바꿔줘야 크래시가 안난다.
			if (InnerObj->IsA(UParticleModule::StaticClass()))
			{
				InnerObj->Rename(NULL, InObj);
			}

			if (InnerObj->IsAComponent())
			{
				// 아웃터 안에서 유니크하게 만든다.
				InnerObj->MakeUniqueObjectName(InObj, InnerObj->GetClass());
			}
		}
	}
}

struct FRestoreWavePlayerData
{
	USoundNode** OriginChildNode;
	USoundNode* SoundNode;
	USoundNodeWavePlayer* WavePlayer;

	FRestoreWavePlayerData(USoundNode** InOrginChildNode, USoundNode* InSoundNode, USoundNodeWavePlayer* InWavePlayer)
	{
		OriginChildNode = InOrginChildNode;
		SoundNode = InSoundNode;
		WavePlayer = InWavePlayer;
	}
};

TArray<FRestoreWavePlayerData> ResotoreWavePlayerDataList;

void WxDlgExportToUE4::SoundNodeWaveToSoundNodeWavePlayer(USoundCue* InSoundCue, USoundNode* InSoundNode)
{
	for (INT i = 0; i < InSoundNode->ChildNodes.Num(); ++i)
	{
		if (InSoundNode->ChildNodes(i))
		{
			USoundNodeWave* SoundNodeWave = Cast<USoundNodeWave>(InSoundNode->ChildNodes(i));
			if (SoundNodeWave)
			{
				USoundNodeWavePlayer* WavePlayer = ConstructObject<USoundNodeWavePlayer>(USoundNodeWavePlayer::StaticClass(), InSoundCue);
				WavePlayer->SoundWaveAssetPtr = SoundNodeWave;
				InSoundNode->ChildNodes(i) = WavePlayer;

				// 나중에 돌려놓는다.
				//ResotoreWavePlayerDataList.AddItem(FRestoreWavePlayerData(&InSoundNode->ChildNodes(i), SoundNodeWave, WavePlayer));

				FSoundNodeEditorData* EditorData = InSoundCue->EditorData.Find(SoundNodeWave);
				if (EditorData)
				{
					FSoundNodeEditorData WavePlayerEditorData;
					WavePlayerEditorData.NodePosX = EditorData->NodePosX;
					WavePlayerEditorData.NodePosY = EditorData->NodePosY;

					InSoundCue->EditorData.Set(WavePlayer, WavePlayerEditorData);
				}
			}
			else
			{
				SoundNodeWaveToSoundNodeWavePlayer(InSoundCue, InSoundNode->ChildNodes(i));
			}
		}
	}
}

void WxDlgExportToUE4::ExportObjectMetaData(UObject* InObj, TArray<UObject*>& OutObjectsToExport, const FString& InPath, UBOOL bUseProvidedExportPath)
{
	if (InObj == nullptr)
		return;
	
	GObjectExportForUE4 = InObj;
	TSet<FString> NameSet;

	MakeUniqueNameInParticleModule(InObj);

	// 사운드 큐
	USoundCue* SoundCue = Cast<USoundCue>(InObj);
	if (SoundCue && SoundCue->FirstNode)
	{
		SoundNodeWaveToSoundNodeWavePlayer(SoundCue, SoundCue->FirstNode);
	}


	FSupportClassInfo* SupportInfo = GetSupportClassInfo(InObj);
	check(SupportInfo);

	if (InObj->IsA(UAnimSequence::StaticClass()))
	{
		ExportAnimSequence(InObj, InPath, bUseProvidedExportPath);
	}
	else
	{
		if (ExportObjectMetaDataInternal(InObj, SupportInfo->ExportClass, InPath, SupportInfo->ExtName, bUseProvidedExportPath))
		{
			if (SupportInfo->bHasResource)
			{
				ConditionalAddForExport(InObj, OutObjectsToExport);
			}
		}
	}

	GObjectExportForUE4 = nullptr;;
}

void WxDlgExportToUE4::OnSaveAllAssetClick(wxCommandEvent& event)
{
	FFilename savePath(TextSaveFolder->GetLabel());
	if (savePath.Len() == 0)
	{
		return;
	}

	const FScopedBusyCursor BusyCursor;

	GExportForUE4 = TRUE;
	GExportUntypedBulkDataForUE4 = FALSE;
	ObjectTools::GForceExportGroupsAsSubDirs = TRUE;

	// 모든 익스포터를 찾아 놓는다. 원래 코드에서는 이 함수가 호출할때 마다 모든 익스포터가 생성되었다. 한번만 되게 한다.
	ObjectTools::AssembleListOfExporters(ObjectTools::GAssembleListOfExporters);

	TArray<FString> PackageList = GPackageFileCache->GetPackageFileList();

	INT packageListNum = PackageList.Num();
	for (INT PackageIndex = 0; PackageIndex < packageListNum; PackageIndex++)
	{
		// 엔진 패키지는 패스
		UBOOL bEnginePackage = (PackageList(PackageIndex).InStr(TEXT("..\\..\\Engine")) != -1);
		if (bEnginePackage)
			continue;

		// 맵 패키지도 패스. 나중에 맵에서 직접 뽑는다.
		UBOOL bMapPackage = (PackageList(PackageIndex).InStr(TEXT("Content\\Maps")) != -1);
		if (bMapPackage)
			continue;

		// UI도 패스.
		UBOOL bUIPackage = (PackageList(PackageIndex).InStr(TEXT("Content\\UI")) != -1);
		if (bUIPackage)
			continue;

		FFilename SrcFilename = PackageList(PackageIndex);

		FString Ext = SrcFilename.GetExtension();

		// 패키지와 맵파일이 아니면 (.u) 패스
		if (Ext != TEXT("upk") && Ext != TEXT("muz"))
			continue;

		UPackage* Package = UObject::LoadPackage(NULL, *SrcFilename, LOAD_None);

		if (!Package)
			continue;

		Package->FullyLoad();

		TextExportedPackage->SetLabel(*SrcFilename);

		// 패스 계산
		FString afterContentStr = GetPathAfterContent(SrcFilename);
		FString lastPath = savePath;

		if (afterContentStr.Len() > 0)
		{
			lastPath = lastPath + PATH_SEPARATOR + afterContentStr;
		}

		TArray<UObject*> ObjectsToExport;

		for (FObjectIterator It; It; ++It)
		{
			UObject* Obj = *It;

			if (!Obj->IsIn(Package))
				continue;

			if (!CheckExportOption(Obj))
			{
				continue;
			}

			if (IgnoreExportForUE4(Obj, nullptr))
				continue;

			if (::GetKeyState(VK_ESCAPE) & 0x8000)
			{
				break;
			}
			
			ExportObjectMetaData(Obj, ObjectsToExport, lastPath);
		}

		ObjectTools::ExportObjects(ObjectsToExport, FALSE, &lastPath, TRUE);

		// 메모리가 늘어나 가비지 콜렉팅을 한다.
		if (PackageIndex % 10 == 0)
		{
			GWorld->ClearComponents();
			// Collect garbage up-front to ensure that only required objects will be put into the seekfree package.
			UObject::CollectGarbage(RF_Native);
		}

		if (::GetKeyState(VK_ESCAPE) & 0x8000)
		{
			break;
		}
	}

	TextExportedPackage->SetLabel(TEXT(""));

	GExportForUE4 = FALSE;
	ObjectTools::GForceExportGroupsAsSubDirs = FALSE;
}
//------------------------------------------------------------------------------------------------------------------

FExportMapCallbackEventDevice* WxDlgExportToUE4::ExportCurrentLevel(const FString& InFilename, const TCHAR* InSaveFilename)
{
	FFilename savePath(TextSaveFolder->GetLabel());
	if (savePath.Len() == 0)
	{
		return nullptr;
	}

	FString ExportFilename;

	if (InSaveFilename)
	{
		ExportFilename = InSaveFilename;
	}
	else
	{
		// 패스 계산
		FString afterContentStr = GetPathAfterContent(InFilename);

		if (afterContentStr.Len() <= 0)
			return nullptr;
	
		FString lastPath = savePath;
		FString Levelname = FFilename(InFilename).GetBaseFilename();
		lastPath = lastPath + PATH_SEPARATOR + afterContentStr;
		ExportFilename = lastPath + PATH_SEPARATOR + Levelname + TEXT(".T3DLV");
	}

	if (!CheckOverWriteOption(*ExportFilename))
		return nullptr;

	return new FExportMapCallbackEventDevice(InFilename, ExportFilename, TextExportedPackage);
}

void WxDlgExportToUE4::OnSaveLevelAssetClick(wxCommandEvent& event)
{
	FFilename savePath(TextSaveFolder->GetLabel());
	if (savePath.Len() == 0)
	{
		return;
	}

	// 모든 익스포터를 찾아 놓는다. 원래 코드에서는 이 함수가 호출할때 마다 모든 익스포터가 생성되었다. 한번만 되게 한다.
	ObjectTools::AssembleListOfExporters(ObjectTools::GAssembleListOfExporters);

	TArray<FString> PackageList = GPackageFileCache->GetPackageFileList();

	//GWarn->BeginSlowTask(*LocalizedExportingMap, TRUE);

	FExportMapCallbackEventDevice* CurrentExportMapCallback = nullptr;
	FExportMapCallbackEventDevice* FirstExportMapCallback = nullptr;

	INT packageListNum = PackageList.Num();
	for (INT PackageIndex = 0; PackageIndex < PackageList.Num(); PackageIndex++)
	{
		UBOOL bMapPackage = (PackageList(PackageIndex).InStr(TEXT("Content\\Maps")) != -1);
		if (!bMapPackage)
		{
			PackageList.Remove(PackageIndex);
			--PackageIndex;
			continue;
		}

		FFilename SrcFilename = PackageList(PackageIndex);

		FString Ext = SrcFilename.GetExtension();

		// 패키지와 맵파일이 아니면 (.u) 패스
		if (Ext != TEXT("muz"))
		{
			PackageList.Remove(PackageIndex);
			--PackageIndex;
			continue;
		}

		FExportMapCallbackEventDevice* NewCallback = ExportCurrentLevel(SrcFilename);

		if (CurrentExportMapCallback)
		{
			CurrentExportMapCallback->Next = NewCallback;
		}
		else
		{
			FirstExportMapCallback = NewCallback;
			CurrentExportMapCallback = NewCallback;
		}

		CurrentExportMapCallback = NewCallback;
	}

	if (FirstExportMapCallback)
		FirstExportMapCallback->LoadMapForExport();
}

UBOOL		GExportForUE4					= FALSE;
UBOOL		GExportUntypedBulkDataForUE4	= FALSE;
UObject*	GObjectExportForUE4				= nullptr;


// 언리얼4에서 이름이 바뀐 클래스들. T3D로 저장할 때 클래스 이름을 4에 맞게 바꿔준다.
FString	ExportClassNameForUE4(const FString& InClassName, const UObject* InObject)
{
	// 머티리얼 표현식이 4에서 없어지거나 구조가 바뀐거는 새로운 이름으로 추가
	const FString* IndirecName= FindRedirectExpressionClassName(InClassName);
	if (IndirecName)
	{
		return *IndirecName;
	}

	// 완전히 이름바뀜
	if (InClassName == TEXT("SoundNodeWave"))
		return FString(TEXT("SoundWave"));
	// 없어짐
	if (InClassName == TEXT("DecalMaterial"))
		return FString(TEXT("Material"));
	// 없어짐
	if (InClassName == TEXT("MaterialInstanceTimeVarying"))
		return FString(TEXT("MaterialInstanceConstant"));
	
	if (InClassName == TEXT("GwNavVolume"))
		return FString(TEXT("Volume"));
	
	if (InClassName == TEXT("GwNavExclusionVolume"))
		return FString(TEXT("BlockingVolume"));
	
	if (InClassName == TEXT("GwNavExplorationVolume"))
		return FString(TEXT("Volume"));
	
	// UE4 CoreRedirects.cpp -> CLASS_REDIRECT
	if (InClassName == TEXT("InterpActor"))
		return FString(TEXT("StaticMeshActor"));
	
	if (InClassName == TEXT("PointLightMovable"))
		return FString(TEXT("PointLight"));
	
	if (InClassName == TEXT("DominantPointLight"))
		return FString(TEXT("PointLight"));
	
	if (InClassName == TEXT("PointLightToggleable"))
		return FString(TEXT("PointLight"));
	
	if (InClassName == TEXT("DominantDirectionalLightComponent"))
		return FString(TEXT("DirectionalLightComponent"));

	if (InClassName == TEXT("MaterialExpressionTerrainLayerCoords"))
		return FString(TEXT("MaterialExpressionLandscapeLayerCoords"));

	if (InClassName == TEXT("MaterialExpressionTerrainLayerSwitch"))
		return FString(TEXT("MaterialExpressionLandscapeLayerSwitch"));

	if (InClassName == TEXT("MaterialExpressionTerrainLayerWeight"))
		return FString(TEXT("MaterialExpressionLandscapeLayerWeight"));

	if (InClassName == TEXT("MU2ParticleModuleTypeDataMesh"))
		return FString(TEXT("ParticleModuleTypeDataMesh"));

	if (InClassName == TEXT("MU2ParticleModuleSound"))
		return FString(TEXT("ParticleModuleLocation"));

	if (InClassName == TEXT("MU2ParticleModuleLocationEmitter"))
		return FString(TEXT("ParticleModuleLocationEmitter"));
	
	if (InClassName == TEXT("ParticleModuleTypeDataMeshPhysX"))
		return FString(TEXT("ParticleModuleTypeDataMesh"));

	if (InClassName == TEXT("DominantDirectionalLight") ||
		InClassName == TEXT("DirectionalLightToggleable"))
		return FString(TEXT("DirectionalLight"));

	if (InClassName == TEXT("DominantPointLight") ||
		InClassName == TEXT("PointLightMovable") ||
		InClassName == TEXT("PointLightToggleable"))
		return FString(TEXT("PointLight"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm"))
		return FString(TEXT("AnimCompress"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_Automatic"))
		return FString(TEXT("AnimCompress_Automatic"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_BitwiseCompressOnly"))
		return FString(TEXT("AnimCompress_BitwiseCompressOnly"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_LeastDestructive"))
		return FString(TEXT("AnimCompress_LeastDestructive"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_PerTrackCompression"))
		return FString(TEXT("AnimCompress_PerTrackCompression"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_RemoveEverySecondKey"))
		return FString(TEXT("AnimCompress_RemoveEverySecondKey"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_RemoveLinearKeys"))
		return FString(TEXT("AnimCompress_RemoveLinearKeys"));

	if (InClassName == TEXT("AnimationCompressionAlgorithm_RemoveTrivialKeys"))
		return FString(TEXT("AnimCompress_RemoveTrivialKeys"));

	if (InClassName == TEXT("DistributionVectorUniformRange"))
		return FString(TEXT("DistributionVectorUniform"));

	//2017-12-19 shimwoosung 이름변경
	if (InClassName == TEXT("InstancedFoliageSettings"))
		return FString(TEXT("FoliageType_InstancedStaticMesh"));	

	//2018-01-30 shimwoosung 이름변경
	if (InClassName == TEXT("RB_BodySetup"))
		return FString(TEXT("BodySetup"));

	if (InClassName.InStr(TEXT("ExponentialHeightFogComponent")) != -1)
	{
		const UComponent* Component = ConstCast<UComponent>(InObject);		
		if (Component)
		{
			return Component->GetInstanceMapName().ToString();
		}
	}

	if (InClassName.InStr(TEXT("BrushComponent")) != -1)
	{
		const UComponent* Component = ConstCast<UComponent>(InObject);
		if (Component)
		{
			UObject* aOuter = InObject->GetOuter();
			if (aOuter && aOuter->IsA(AVolume::StaticClass()))
			{
				return Component->GetInstanceMapName().ToString();				
			}
		}
	}
	
	return InClassName;
}

UObject* ReplaceNeedObject(UObject* InObj) 
{
	UObject* rInObj = InObj;

	if (InObj->IsA(UTexture2D::StaticClass()))
	{
		UTexture2D * aTexture2D = Cast<UTexture2D>(InObj);
		if (aTexture2D)
		{
			if (aTexture2D->MipsToRemoveOnCompress > 0)
			{
				UTexture2D * aCopyTexture2D = (UTexture2D*)UObject::StaticDuplicateObject(InObj, NULL, UObject::GetTransientPackage(), *InObj->GetName());
				aCopyTexture2D->MipsToRemoveOnCompress = 0;
				aCopyTexture2D->Compress();
				aCopyTexture2D->SourceArt.RemoveBulkData();
				rInObj = aCopyTexture2D;
			}
		}
	}

	return rInObj;
}

// 클래스 이름, 구조 등이 바뀌어 아웃터를 바꾼다.
void ExportScriptPathRedirection(const FString& InClassName, FString& OutFinalPath)
{
	// 머티리얼 표현식이 4에서 없어지거나 구조가 바뀐거는 새로운 이름으로 추가하였기 때문에 엔진 패스가 아니라 게임패스로 해준다.
	if (FindRedirectExpressionClassName(InClassName))
	{
		OutFinalPath = OutFinalPath.Replace(TEXT("/Script/Engine."), TEXT("/Script/MULegend."));
	}
	else
	if (InClassName == TEXT("MU2ParticleModuleTypeDataMesh") ||
		InClassName == TEXT("MU2ParticleModuleSound") ||
		InClassName == TEXT("MU2ParticleModuleLocationEmitter"))
	{
		OutFinalPath = OutFinalPath.Replace(TEXT("/Script/MU2Game."), TEXT("/Script/Engine."));
	}
	else
	if (InClassName == TEXT("Landscape") ||
		InClassName == TEXT("LandscapeComponent") ||
		InClassName == TEXT("LandscapeGizmoActor") ||
		InClassName == TEXT("LandscapeGizmoRenderComponent") ||
		InClassName == TEXT("LandscapeGrassType") ||
		InClassName == TEXT("LandscapeHeightfieldCollisionComponent") ||
		InClassName == TEXT("LandscapeInfo") ||
		InClassName == TEXT("LandscapeInfoMap") ||
		InClassName == TEXT("LandscapeLayerInfoObject") ||
		InClassName == TEXT("LandscapeMaterialInstanceConstant") ||
		InClassName == TEXT("LandscapeMeshCollisionComponent") ||
		InClassName == TEXT("LandscapeMeshProxyActor") ||
		InClassName == TEXT("LandscapeMeshProxyComponent") ||
		InClassName == TEXT("LandscapeProxy") ||
		InClassName == TEXT("LandscapeSplineControlPoint") ||
		InClassName == TEXT("LandscapeSplinesComponent") ||
		InClassName == TEXT("LandscapeSplineSegment") ||
		InClassName == TEXT("LandscapeStreamingProxy") ||
		InClassName == TEXT("LandscapeStreamingProxy") ||
		InClassName == TEXT("LandscapeGizmoActiveActor")||
		InClassName == TEXT("MaterialExpressionTerrainLayerCoords") ||
		InClassName == TEXT("MaterialExpressionTerrainLayerSwitch") ||
		InClassName == TEXT("MaterialExpressionTerrainLayerWeight"))
	{
		OutFinalPath = OutFinalPath.Replace(TEXT("/Script/Engine."), TEXT("/Script/Landscape."));
	}
	else
	if (InClassName == TEXT("GwNavVolume") ||
		InClassName == TEXT("GwNavExclusionVolume") ||
		InClassName == TEXT("GwNavExplorationVolume"))
	{
		OutFinalPath = OutFinalPath.Replace(TEXT("/Script/GwNavEngine."), TEXT("/Script/Engine."));
	}
	//2017-12-19 shimwoosung InstancedFoliageActor 관련 아웃터 변경
	else
	if (InClassName == TEXT("InstancedFoliageActor") ||
		InClassName == TEXT("Default__InstancedFoliageActor") ||
		InClassName == TEXT("InstancedFoliageSettings") )
	{
		OutFinalPath = OutFinalPath.Replace(TEXT("/Script/Engine."), TEXT("/Script/Foliage."));
	}
}

// 언리얼 4로 익스포트 하지 않는 오브젝트
UBOOL IgnoreExportForUE4(const UObject* InObj, const UObject* ContainObj)
{
	if (InObj == nullptr)
		return TRUE;

	if (InObj->IsA(UTexture::StaticClass()))
	{
		// 프리팹 프리뷰 이미지
		if (Cast<UPrefab>(InObj->GetOuter()))
			return TRUE;

		// 파티클 썸네일 이미지
		if (Cast<UParticleSystem>(InObj->GetOuter()))
			return TRUE;
	}

	if (InObj->IsA(UShadowMapTexture2D::StaticClass()) ||
		// 언리얼4에서 없어진 클래스
		InObj->IsA(UDrawLightRadiusComponent::StaticClass()) ||
		InObj->IsA(UCylinderComponent::StaticClass()) ||
		InObj->IsA(USpriteComponent::StaticClass()) ||
		InObj->IsA(UShadowMap2D::StaticClass()) ||
		InObj->IsA(UShadowMap1D::StaticClass()) ||
		InObj->IsA(UAnimNodeSequence::StaticClass()) ||
		InObj->IsA(UPathRenderingComponent::StaticClass()) ||
		InObj->IsA(UPreviewMaterial::StaticClass()) ||
		InObj->IsA(UDynamicLightEnvironmentComponent::StaticClass()))
	{
		return TRUE;
	}

	//2017-12-19 shimwoosung 컨테이너 체크
	if (ContainObj)
	{
		if (ContainObj->IsA(AInstancedFoliageActor::StaticClass()))
		{
			if (InObj->IsA(UInstancedStaticMeshComponent::StaticClass()))
			{
				return TRUE;
			}
		}
	}

	return FALSE;
}

UBOOL IgnorePropertyForUE4(const UObject* InObj, const UProperty* InProperty)
{
	FString PropertyName = InProperty->GetName();

	if (PropertyName == TEXT("NavdataFamilies"))
		return TRUE;

	if (InObj->IsA(AActor::StaticClass()))
	{
		if (PropertyName == TEXT("Components"))
			return TRUE;		
	}
	else if (InObj->IsA(UStaticMeshComponent::StaticClass()))
	{
		if (PropertyName == TEXT("LODData"))
			return TRUE;
	}	

	return FALSE;
}

struct FSoundCueGraph;

struct FSoundPin
{
	FGuid PinId;
	FString PinName;

	FSoundPin* LinkPin;
	FSoundCueGraph* LinkGraph;

	FSoundPin()
	{
		PinId.A = PinId.B = PinId.C = PinId.D = 0;
		PinName = TEXT("");
		LinkPin = nullptr;
		LinkGraph = nullptr;
	}

	FSoundPin(FString InPinName, FGuid InGuid, FSoundPin* InLinkPin, FSoundCueGraph* InLinkGraph)
	{
		PinId = InGuid;
		PinName = InPinName;
		LinkPin = InLinkPin;
		LinkGraph = InLinkGraph;
	}
};

struct FSoundCueGraph
{
	FString GraphName;
	UObject* SoundNode;
	INT NodePosX;
	INT NodePosY;
	FGuid NodeGuid;

	FSoundPin OutputPin;
	TArray<FSoundPin*> ExtraOutputPinList;
	TArray<FSoundPin*> InputPinList;

	FSoundCueGraph(FString InGraphName, USoundNode* InSoundNode, INT InNodePosX, INT InNodePosY, FGuid InNodeGuid)
	{
		GraphName = InGraphName;
		SoundNode = InSoundNode;
		NodePosX = InNodePosX;
		NodePosY = InNodePosY;
		NodeGuid = InNodeGuid;
	}

	FSoundPin* AddInput(FString InPinName, FGuid InGuid, FSoundPin* InLinkPin, FSoundCueGraph* InLinkGraph)
	{
		FSoundPin* NewInput = new FSoundPin(InPinName, InGuid, InLinkPin, InLinkGraph);
		InputPinList.AddItem(NewInput);
		return InputPinList.Last();
	}

	INT AddOutput(FString InPinName, FGuid InGuid, FSoundPin* InLinkPin, FSoundCueGraph* InLinkGraph)
	{
		if (OutputPin.PinId.IsValid() == FALSE)
		{
			OutputPin = FSoundPin(InPinName, InGuid, InLinkPin, InLinkGraph);
			return 1;
		}
	
		FSoundPin* NewOutput = new FSoundPin(InPinName, InGuid, InLinkPin, InLinkGraph);
		ExtraOutputPinList.AddItem(NewOutput);

		return ExtraOutputPinList.Num() + 1;
	}
};

INT GSoundNodeGraphCount = 0;
TMap<UObject*, FSoundCueGraph*> GSoundNodeCueGraphMap;

void _ConnectSoundNodeGraph(USoundCue* InSoundCue, FSoundCueGraph* InParentGraph, FSoundPin* InParentPin, USoundNode* InChild)
{
	if (InChild == nullptr)
		return;

	FSoundCueGraph* CueGraph = GSoundNodeCueGraphMap.FindRef(InChild);
	check(CueGraph);

	INT OutputNum = CueGraph->AddOutput(TEXT("Output"), appCreateGuid(), InParentPin, InParentGraph);
	
	//OutputNum 이 1이면 인풋 설정을 아직 안했다.
	if (OutputNum == 1)
	{
		for (INT i = 0; i < InChild->ChildNodes.Num(); ++i)
		{
			FString PinName = FString::Printf(TEXT("Input%d"), i);

			FSoundCueGraph* ConnectCueGraph = InChild->ChildNodes(i) ? GSoundNodeCueGraphMap.FindRef(InChild->ChildNodes(i)) : nullptr;

			FSoundPin* LastInputPin = CueGraph->AddInput(PinName, appCreateGuid(), (ConnectCueGraph ? &ConnectCueGraph->OutputPin : nullptr), ConnectCueGraph);
			_ConnectSoundNodeGraph(InSoundCue, CueGraph, LastInputPin, InChild->ChildNodes(i));
		}
	}
}

void _CreateSoundNodeGraph(USoundCue* InSoundCue, USoundNode* InSoundNode)
{
	if (InSoundNode == nullptr)
		return;

	FSoundCueGraph* CueGraph = GSoundNodeCueGraphMap.FindRef(InSoundNode);
	if (CueGraph == nullptr)
	{
		FString GraphName = FString::Printf(TEXT("SoundCueGraphNode_%d"), GSoundNodeGraphCount++);
		
		INT NodePosX = 0, NodePosY = 0;
		FSoundNodeEditorData* EditorData = InSoundCue->EditorData.Find(InSoundNode);
		if (EditorData)
		{
			NodePosX = EditorData->NodePosX;
			NodePosY = EditorData->NodePosY;
		}

		FSoundCueGraph* NewSoundCueGraph = new FSoundCueGraph(GraphName, InSoundNode, NodePosX, NodePosY, appCreateGuid());
		GSoundNodeCueGraphMap.Set(InSoundNode, NewSoundCueGraph);
	}

	for (INT i = 0; i < InSoundNode->ChildNodes.Num(); ++i)
	{
		_CreateSoundNodeGraph(InSoundCue, InSoundNode->ChildNodes(i));
	}
}

void PreExportObjectInnerForUE4(UObject* InObj, FOutputDevice& Out)
{
	// 사운드 큐
	USoundCue* SoundCue = Cast<USoundCue>(InObj);
	if (SoundCue)
	{
		GSoundNodeGraphCount = 0;
		GSoundNodeCueGraphMap.Empty();
		
		if (SoundCue->FirstNode)
		{
			_CreateSoundNodeGraph(SoundCue, SoundCue->FirstNode);

			FString GraphName = TEXT("SoundCueGraphNode_Root_0");

			INT NodePosX = 0, NodePosY = 0;
			FSoundCueGraph* NewSoundCueGraph = new FSoundCueGraph(GraphName, nullptr, NodePosX, NodePosY, appCreateGuid());
			GSoundNodeCueGraphMap.Set(SoundCue, NewSoundCueGraph);
			FSoundCueGraph* CueGraph = GSoundNodeCueGraphMap.FindRef(SoundCue);

			FSoundCueGraph* LinkCueGraph = GSoundNodeCueGraphMap.FindRef(SoundCue->FirstNode);
			CueGraph->AddInput(TEXT("Input"), appCreateGuid(), &LinkCueGraph->OutputPin, LinkCueGraph);

			_ConnectSoundNodeGraph(SoundCue, CueGraph, CueGraph->InputPinList(0), SoundCue->FirstNode);

			// 미리 정의해 놓는다.
			for (TObjectIterator<USoundNode> It; It; ++It)
			{
				USoundNode* SoundNode = *It;
				if (SoundNode->IsIn(InObj))
				{
					Out.Logf(TEXT("Begin Object Class=/Script/Engine.%s Name=\"%s\"\n"), *SoundNode->GetClass()->GetName(), *SoundNode->GetName());
					Out.Logf(TEXT("End Object\n"));
				}
			}

			Out.Logf(TEXT("Begin Object Class=/Script/AudioEditor.SoundCueGraph Name=\"SoundCueGraph_0\"\n"));
			Out.Logf(TEXT("Begin Object Class=/Script/AudioEditor.SoundCueGraphNode_Root Name=\"SoundCueGraphNode_Root_0\"\n"));
			Out.Logf(TEXT("End Object\n"));

			for (TMap<UObject*, FSoundCueGraph*>::TIterator It(GSoundNodeCueGraphMap); It; ++It)
			{
				FSoundCueGraph* CueGraph = It.Value();

				if (CueGraph->GraphName != TEXT("SoundCueGraphNode_Root_0"))
				{
					FString EmitGraph = FString::Printf(TEXT("Begin Object Class=/Script/AudioEditor.SoundCueGraphNode Name=\"%s\"\n"), *CueGraph->GraphName);
					Out.Logf(*EmitGraph);
					Out.Logf(TEXT("End Object\n"));
				}
			}

			Out.Logf(TEXT("End Object\n"));
		}

		return;
	}
}

void PreExportPropertiesForUE4(UObject* InObj, FOutputDevice& Out)
{
	
}

void PostExportPropertiesForUE4(UObject* InObj, FOutputDevice& Out)
{
	// 사운드 큐
	USoundCue* SoundCue = Cast<USoundCue>(InObj);
	if (SoundCue)
	{
		Out.Logf(TEXT("Begin Object Name=\"SoundCueGraph_0\"\n"));

		// 사운드 그래프
		for (TMap<UObject*, FSoundCueGraph*>::TIterator It(GSoundNodeCueGraphMap); It; ++It)
		{
			FSoundCueGraph* CueGraph = It.Value();

			FString EmitGraph = FString::Printf(TEXT("Begin Object Name=\"%s\"\n"), *CueGraph->GraphName);
			Out.Logf(*EmitGraph);

			if (CueGraph->GraphName != TEXT("SoundCueGraphNode_Root_0"))
			{
				FString SoundNodeClassName = CueGraph->SoundNode->GetClass()->GetName();
				if (SoundNodeClassName == TEXT("SoundNodeWave"))
				{
					SoundNodeClassName = TEXT("SoundNodeWavePlayer");
				}

				FString EmitSoundNode = FString::Printf(TEXT("SoundNode=%s'%s:%s'\n"), *SoundNodeClassName, *CueGraph->SoundNode->GetOuter()->GetName(), *CueGraph->SoundNode->GetName());
				Out.Logf(*EmitSoundNode);
			}

			Out.Logf(*FString::Printf(TEXT("NodePosX=%d\n"), -CueGraph->NodePosX));
			Out.Logf(*FString::Printf(TEXT("NodePosY=%d\n"), CueGraph->NodePosY));
			Out.Logf(*FString::Printf(TEXT("NodeGuid=%s\n"), *CueGraph->NodeGuid.String()));

			if (CueGraph->GraphName == TEXT("SoundCueGraphNode_Root_0"))
			{
				if (CueGraph->InputPinList.Num())
				{
					Out.Logf(TEXT("CustomProperties Pin "));
					FSoundPin& Pin = *CueGraph->InputPinList(0);

					FString PinProperty;

					if (Pin.LinkGraph && Pin.LinkPin)
					{
						PinProperty = FString::Printf(TEXT("(PinId=%s,PinType.PinCategory=\"SoundNode\",PinType.PinSubCategory=\"Root\",LinkedTo=(%s %s,),)\n"), *Pin.PinId.String(), *Pin.LinkGraph->GraphName, *Pin.LinkPin->PinId.String());
					}
					else
					{
						PinProperty = FString::Printf(TEXT("(PinId=%s,PinType.PinCategory=\"SoundNode\",PinType.PinSubCategory=\"Root\")\n"), *Pin.PinId.String());
					}
					Out.Logf(*PinProperty);
				}
			}
			else
			{
				for (INT i = 0; i < CueGraph->InputPinList.Num(); ++i)
				{
					Out.Logf(TEXT("CustomProperties Pin "));
					FSoundPin& Pin = *CueGraph->InputPinList(i);

					FString PinProperty;

					if (Pin.LinkGraph && Pin.LinkPin)
					{
						PinProperty = FString::Printf(TEXT("(PinId=%s,PinName=\"Input%d\",PinType.PinCategory=\"SoundNode\",LinkedTo=(%s %s,),)\n"), *Pin.PinId.String(), i, *Pin.LinkGraph->GraphName, *Pin.LinkPin->PinId.String());
					}
					else
					{
						PinProperty = FString::Printf(TEXT("(PinId=%s,PinName=\"Input%d\",PinType.PinCategory=\"SoundNode\")\n"), *Pin.PinId.String(), i);
					}
					Out.Logf(*PinProperty);
				}

				Out.Logf(TEXT("CustomProperties Pin "));
				FSoundPin& Pin = CueGraph->OutputPin;

				FString PinProperty;

				if (Pin.LinkGraph && Pin.LinkPin)
				{
					FString LinkedToStr = FString::Printf(TEXT("LinkedTo=(%s %s"), *Pin.LinkGraph->GraphName, *Pin.LinkPin->PinId.String());

					// 아웃풋도 여러개로 연결될 수 있다. 붙여서 나온다.
					for (INT ExtraIndex = 0; ExtraIndex < CueGraph->ExtraOutputPinList.Num(); ++ExtraIndex)
					{
						FSoundPin& ExtraPin = *CueGraph->ExtraOutputPinList(ExtraIndex);

						if (ExtraPin.LinkGraph && ExtraPin.LinkPin)
						{
							LinkedToStr = LinkedToStr + TEXT(",") + ExtraPin.LinkGraph->GraphName + TEXT(" ") + ExtraPin.LinkPin->PinId.String();
						}
					}

					PinProperty = FString::Printf(TEXT("(PinId=%s,PinName=\"Output\",Direction=\"EGPD_Output\",PinType.PinCategory=\"SoundNode\",%s,),)\n"), *Pin.PinId.String(), *LinkedToStr);
				}
				else
				{
					PinProperty = FString::Printf(TEXT("(PinId=%s,PinName=\"Output\",Direction=\"EGPD_Output\",PinType.PinCategory=\"SoundNode\",)\n"), *Pin.PinId.String());
				}
				Out.Logf(*PinProperty);
			}

			Out.Logf(TEXT("End Object\n"));
		}

		Out.Logf(TEXT("Schema=Class'/Script/AudioEditor.SoundCueGraphSchema'\n"));

		TArray<FSoundCueGraph*> SortedGraphList;

		for (TMap<UObject*, FSoundCueGraph*>::TIterator It(GSoundNodeCueGraphMap); It; ++It)
		{
			FSoundCueGraph* CueGraph = It.Value();

			if (CueGraph->GraphName == TEXT("SoundCueGraphNode_Root_0"))
			{
				SortedGraphList.InsertItem(CueGraph, 0);
			}
			else
			{
				SortedGraphList.AddItem(CueGraph);
			}
		}

		for(INT i = 0; i < SortedGraphList.Num(); ++i)
		{
			FSoundCueGraph* CueGraph = SortedGraphList(i);

			FString NodeList;

			if (CueGraph->GraphName == TEXT("SoundCueGraphNode_Root_0"))
			{
				NodeList = FString::Printf(TEXT("Nodes(%d)=SoundCueGraphNode_Root'%s'\n"), i, *CueGraph->GraphName);
			}
			else
			{
				NodeList = FString::Printf(TEXT("Nodes(%d)=SoundCueGraphNode'%s'\n"), i, *CueGraph->GraphName);
			}

			Out.Logf(*NodeList);
		}

		Out.Logf(TEXT("bAllowDeletion=False\n"));

		FGuid guid = appCreateGuid();
		Out.Logf(*FString::Printf(TEXT("GraphGuid=%s\n"), *guid.String()));

		Out.Logf(TEXT("End Object\n"));
		//-----------------------------------------------------------------------------------------------------------------------------------------------
		
		TArray<USoundNode*> AllNodes;
		for (TObjectIterator<USoundNode> It; It; ++It)
		{
			if ((*It)->IsIn(InObj))
			{
				AllNodes.AddItem(*It);
			}
		}

		for (INT i = 0; i < AllNodes.Num(); ++i)
		{
			USoundNode* SoundNode = AllNodes(i);
			Out.Logf(TEXT("AllNodes(%d)=%s'%s'\n"), i, *SoundNode->GetClass()->GetName(), *SoundNode->GetName());
		}

		Out.Logf(TEXT("SoundCueGraph=SoundCueGraph'SoundCueGraph_0'\n"));

		for (TMap<UObject*, FSoundCueGraph*>::TIterator It(GSoundNodeCueGraphMap); It; ++It)
		{
			FSoundCueGraph* CueGraph = It.Value();

			for (INT i = 0; i < CueGraph->InputPinList.Num(); ++i)
			{
				delete CueGraph->InputPinList(i);
			}
			for (INT i = 0; i < CueGraph->ExtraOutputPinList.Num(); ++i)
			{
				delete CueGraph->ExtraOutputPinList(i);
			}

			delete CueGraph;
		}

		for (INT i = 0; i < ResotoreWavePlayerDataList.Num(); ++i)
		{
			FRestoreWavePlayerData& WavePlayerData = ResotoreWavePlayerDataList(i);
			*WavePlayerData.OriginChildNode = WavePlayerData.SoundNode;
		}

		for (TMap<USoundNode*,FSoundNodeEditorData>::TIterator It(SoundCue->EditorData); It; ++It)
		{
			USoundNodeWavePlayer* WavePlayer = Cast<USoundNodeWavePlayer>(It.Key());
			if (WavePlayer)
			{
				SoundCue->EditorData.Remove(WavePlayer);
				WavePlayer->MarkPendingKill();
			}
		}


		return;
	}

	USoundNode* SoundNode = Cast<USoundNode>(InObj);
	if (SoundNode)
	{
		FSoundCueGraph* CueGraph = GSoundNodeCueGraphMap.FindRef(SoundNode);
		if (CueGraph)
		{
			FString EmitGraphNode = FString::Printf(TEXT("GraphNode=SoundCueGraphNode'%s:SoundCueGraph_0.%s'\n"), *SoundNode->GetOuter()->GetName(), *CueGraph->GraphName);
			Out.Logf(*EmitGraphNode);
		}

		return;
	}

	UAnimSequence* AnimSequence = Cast<UAnimSequence>(InObj);
	if (AnimSequence)
	{
		if (AnimSequence->GetOuter())
		{
			UAnimSet* AnimSet = AnimSequence->GetAnimSet();

			if (AnimSet)
			{
				USkeletalMesh* SkelMesh = LoadObject<USkeletalMesh>(NULL, *AnimSet->PreviewSkelMeshName.ToString(), NULL, LOAD_None, NULL);
				if (SkelMesh)
				{
					// 4에서 임포트하면 스켈메시 이름에 "_Skeleton"이 붙어서 스켈레톤이 생성된다.
					// 애니메이션 임포트할 때 쓰일 스켈레톤 정보를 저장한다.
					FString SkeletonName = SkelMesh->GetName() + TEXT("_Skeleton");
					FFilename SkelMeshPath = SkelMesh->GetPathName();
					FString SkeletonPath = SkelMeshPath.GetPath() + TEXT("/") + SkeletonName + TEXT(".") + SkeletonName;
					Out.Logf(TEXT("Skeleton=%s\n"), *SkeletonPath);
				}
			}
		}

		return;
	}

	ULandscapeComponent* LandscapeComp = Cast<ULandscapeComponent>(InObj);
	ULandscapeHeightfieldCollisionComponent* HeightfieldComp = Cast<ULandscapeHeightfieldCollisionComponent>(InObj);
	if (LandscapeComp || HeightfieldComp)
	{
		FVector RelativeLocation(0);
		
		RelativeLocation.X = LandscapeComp ? LandscapeComp->SectionBaseX : HeightfieldComp->SectionBaseX;
		RelativeLocation.Y = LandscapeComp ? LandscapeComp->SectionBaseY : HeightfieldComp->SectionBaseY;

		Out.Logf(TEXT("RelativeLocation=(X=%6f,Y=%6f,Z=%6f)\n"), RelativeLocation.X, RelativeLocation.Y, RelativeLocation.Z);

		Out.Logf(TEXT("AttachParent=%s\n"), TEXT("RootComponent0"));

		if (LandscapeComp && LandscapeComp->MaterialInstance)
		{
			Out.Logf(TEXT("MaterialInstances(0)=LandscapeMaterialInstanceConstant'%s'\n"), *LandscapeComp->MaterialInstance->GetName());
		}

		return;
	}

	ALandscape* Landscape = Cast<ALandscape>(InObj);
	if (Landscape)
	{
		FLOAT YawDeg = Landscape->Rotation.Yaw * 0.00549316540360483;
		FLOAT PitchDeg = Landscape->Rotation.Pitch * 0.00549316540360483;
		FLOAT RollDeg = Landscape->Rotation.Roll * 0.00549316540360483;
		FVector Scale = Landscape->DrawScale * Landscape->DrawScale3D;

		Out.Logf(TEXT("Begin Object Name=RootComponent0\n"));
		Out.Logf(TEXT("RelativeLocation=(X=%6f,Y=%6f,Z=%6f)\n"), Landscape->Location.X, Landscape->Location.Y, Landscape->Location.Z);
		Out.Logf(TEXT("RelativeRotation=(Pitch=%6f,Yaw=%6f,Roll=%6f)\n"), PitchDeg, YawDeg, RollDeg);
		Out.Logf(TEXT("RelativeScale3D=(X=%6f,Y=%6f,Z=%6f)\n"), Scale.X, Scale.Y, Scale.Z);
		Out.Logf(TEXT("RootComponent=RootComponent0\n"));
		Out.Logf(TEXT("End Object"));
		return;
	}

	UActorComponent* ActorComp = Cast<UActorComponent>(InObj);
	if (ActorComp)
	{
		AActor* Actor = ActorComp->GetOwner();
		
		if (Actor == nullptr)
			Actor = Cast<AActor>(ActorComp->GetOuter());

		if (Actor)
		{
			if (Actor->Location.IsZero() == FALSE)
			{
				Out.Logf(TEXT("RelativeLocation=(X=%6f,Y=%6f,Z=%6f)\n"), Actor->Location.X, Actor->Location.Y, Actor->Location.Z);
			}

			if (Actor->Rotation.IsZero() == FALSE)
			{
				FLOAT YawDeg = Actor->Rotation.Yaw * 0.00549316540360483;
				FLOAT PitchDeg = Actor->Rotation.Pitch * 0.00549316540360483;
				FLOAT RollDeg = Actor->Rotation.Roll * 0.00549316540360483;
				Out.Logf(TEXT("RelativeRotation=(Pitch=%6f,Yaw=%6f,Roll=%6f)\n"), PitchDeg, YawDeg, RollDeg);
			}

			FVector Scale = Actor->DrawScale * Actor->DrawScale3D;
			if (Scale.X != 1.0f || Scale.Y != 1.0f || Scale.Z != 1.0f)
			{
				Out.Logf(TEXT("RelativeScale3D=(X=%6f,Y=%6f,Z=%6f)\n"), Scale.X, Scale.Y, Scale.Z);
			}
		}

		UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(InObj);
		if (StaticMeshComp)
		{
			for (INT i = 0; i < StaticMeshComp->Materials.Num(); ++i)
			{
				UMaterialInterface* MaterialInterface = StaticMeshComp->Materials(i);
				if (MaterialInterface)
				{
					Out.Logf(*FString::Printf(TEXT("OverrideMaterials(%d)=%s'%s'\n"), i, *MaterialInterface->GetClass()->GetName(), *MaterialInterface->GetPathName()));
				}
			}
		}
	}

	FString RootComponentName;

	AStaticMeshActor* StaticMeshActor = Cast<AStaticMeshActor>(InObj);
	if (StaticMeshActor && StaticMeshActor->StaticMeshComponent)
	{
		RootComponentName = StaticMeshActor->StaticMeshComponent->GetName();
	}

	ASkeletalMeshActor* SkelMeshActor = Cast<ASkeletalMeshActor>(InObj);
	if (SkelMeshActor && SkelMeshActor->SkeletalMeshComponent)
	{
		RootComponentName = SkelMeshActor->SkeletalMeshComponent->GetName();
	}

	AEmitter* Emitter = Cast<AEmitter>(InObj);
	if (Emitter && Emitter->ParticleSystemComponent)
	{
		RootComponentName = Emitter->ParticleSystemComponent->GetName();
	}

	ALight* Light = Cast<ALight>(InObj);
	if (Light && Light->LightComponent)
	{
		APointLight* PointLight = Cast<APointLight>(Light);
		if (PointLight)
		{
			Out.Logf(TEXT("PointLightComponent=%s\n"), *Light->LightComponent->GetName());
		}
		else
		{
			ASpotLight* SpotLight = Cast<ASpotLight>(Light);
			if (SpotLight)
			{
				Out.Logf(TEXT("SpotLightComponent=%s\n"), *Light->LightComponent->GetName());
			}
		}

		RootComponentName = Light->LightComponent->GetName();
	}

	AInterpActor* InterpActor = Cast<AInterpActor>(InObj);
	if (InterpActor && InterpActor->StaticMeshComponent)
	{
		RootComponentName = InterpActor->StaticMeshComponent->GetName();
	}

	//2017-12-19 shimwoosung UInstancedFoliageSettings는 언리얼4에서 FoliageType_InstancedStaticMesh로 대체된다.
	//이때 Foliage StaticMesh도 같이 추가해준다.
	UInstancedFoliageSettings* aInstancedFoliageSettings = Cast<UInstancedFoliageSettings>(InObj);
	if (aInstancedFoliageSettings)
	{
		//2018-01-25 shimwoosung 언리얼4에서 bOverrideLightMapRes 기본값은 true - 이렇게 되면 Static mesh에 지정된 라이트맵 해상도를 그래도 사용한다. 그래서 에러발생
		Out.Logf(TEXT("bOverrideLightMapRes=True\n"));
		AInstancedFoliageActor* aActor = Cast<AInstancedFoliageActor>(InObj->GetOuter());
		if (aActor)
		{
			for (TMap<class UStaticMesh*, struct FFoliageMeshInfo>::TIterator MeshIt(aActor->FoliageMeshes); MeshIt; ++MeshIt)
			{
				FFoliageMeshInfo& MeshInfo = MeshIt.Value();
				UInstancedFoliageSettings* MeshSettings = MeshInfo.Settings;
				if (aInstancedFoliageSettings == MeshSettings)
				{
					UStaticMesh* aMesh = MeshIt.Key();
					if (aMesh)
					{
						Out.Logf(TEXT("StaticMesh=%s\n"), *aMesh->GetPathName());
					}
				}
			}
		}
	}

	//2018-01-19 shimwoosung LightComponent Mobility=Static 기본값 셋팅
	ULightComponent * aLightComponent = Cast<ULightComponent>(InObj);
	if (aLightComponent)
	{
		if (aLightComponent->CastStaticShadows)
		{
			if (!aLightComponent->CastDynamicShadows)
				Out.Logf(TEXT("Mobility=Static\n"));
		}
		else
		{
			if (aLightComponent->CastDynamicShadows)
				Out.Logf(TEXT("Mobility=Movable\n"));
		}		
	}

	//2018-03-19 shimwoosung Level에 UExponentialHeightFogComponent가 존재할시 DirectionalLightComponent에 
	//bUsedAsAtmosphereSunLight=True 설정
	UDirectionalLightComponent * aDirectionalLightComponent = Cast<UDirectionalLightComponent>(InObj);
	if (aDirectionalLightComponent)
	{
		bool IsFindExponentialHeightFogComponent = false;
		for (TObjectIterator<UExponentialHeightFogComponent> It; It; ++It)
		{
			IsFindExponentialHeightFogComponent = true;
			break;
		}

		if (IsFindExponentialHeightFogComponent)
		{
			Out.Logf(TEXT("bUsedAsAtmosphereSunLight=True\n"));
		}
	}

	//2018-01-19 shimwoosung PointLightComponent UseInverseSquaredFalloff 기본값 false로 셋팅
	UPointLightComponent * aPLightComponent = Cast<UPointLightComponent>(InObj);
	if (aPLightComponent)
	{
		Out.Logf(TEXT("bUseInverseSquaredFalloff=False\n"));
	}

	//2018-01-19 shimwoosung UStaticMeshComponent 내로 CollisionType 출력작업
	UStaticMeshComponent * aStaticMeshComponent = Cast<UStaticMeshComponent>(InObj);
	if (aStaticMeshComponent)
	{
		AActor* aActor = Cast<AActor>(InObj->GetOuter());
		if (aActor)
		{
			BYTE aCollisionType = aActor->CollisionType;
			FString sCollisionProfileName = TEXT("Default");
			if (aCollisionType != COLLIDE_CustomDefault)
			{
				//Out.Logf(TEXT("bUseDefaultCollision=False\n"));				
				switch (aCollisionType)
				{
					case COLLIDE_NoCollision:
						sCollisionProfileName = TEXT("NoCollision");
						break;
					case COLLIDE_TouchAll:
						sCollisionProfileName = TEXT("OverlapAll");
						break; 
					case COLLIDE_BlockAll:
						sCollisionProfileName = TEXT("BlockAll");
						break;
					case COLLIDE_TouchAllButWeapons:
						sCollisionProfileName = TEXT("IgnoreOnlyPawn");
						break;
					case COLLIDE_BlockAllButWeapons:
						sCollisionProfileName = TEXT("OverlapOnlyPawn");
						break;
					case COLLIDE_BlockWeapons:
						sCollisionProfileName = TEXT("BlockAllDynamic");
						break;		
				}

				Out.Logf(TEXT("CollisionProfileName=%s\n"), *sCollisionProfileName);
			}
		}
	}

	//2018-03-19 shimwoosung ExponentialHeightFogComponent 추가정보 출력
	UExponentialHeightFogComponent * aExponentialHeightFogComponent = Cast<UExponentialHeightFogComponent>(InObj);
	if (aExponentialHeightFogComponent)
	{
		float fDirectionalInscatteringStartDistance = aExponentialHeightFogComponent->StartDistance;
		Out.Logf(TEXT("DirectionalInscatteringExponent=16.00\n"));
		Out.Logf(TEXT("DirectionalInscatteringStartDistance=%f\n"), fDirectionalInscatteringStartDistance);			
		RootComponentName = aExponentialHeightFogComponent->GetInstanceMapName().ToString();
	}

	if (RootComponentName.Len())
	{
		Out.Logf(TEXT("RootComponent=%s\n"), *RootComponentName);
	}
}

FString ConvertLinearColor(FString aValue, float fMulti, float fDefaultAValue, bool IsSetDefault)
{
	FString aReturn = TEXT("");
	
	float B, R, G, A;
	FString aTempContentStr = aValue;
	FString beforeContentStr, afterContentStr = TEXT("");
	FString beforeTempStr, afterTempStr = TEXT("");
	aTempContentStr.Split(TEXT(","), &beforeContentStr, &afterContentStr, FALSE);
	beforeContentStr.Split(TEXT("="), &beforeTempStr, &afterTempStr, FALSE);
	B = appAtof(*afterTempStr) / 255.0f;
	aTempContentStr = afterContentStr;
	aTempContentStr.Split(TEXT(","), &beforeContentStr, &afterContentStr, FALSE);
	beforeContentStr.Split(TEXT("="), &beforeTempStr, &afterTempStr, FALSE);
	G = appAtof(*afterTempStr) / 255.0f;
	aTempContentStr = afterContentStr;
	aTempContentStr.Split(TEXT(","), &beforeContentStr, &afterContentStr, FALSE);
	beforeContentStr.Split(TEXT("="), &beforeTempStr, &afterTempStr, FALSE);
	R = appAtof(*afterTempStr) / 255.0f;
	aTempContentStr = afterContentStr;	
	afterContentStr.Split(TEXT("="), &beforeTempStr, &afterTempStr, FALSE);
	int nLen = afterTempStr.Len() - 1;
	aTempContentStr = afterTempStr.Left(nLen);
	
	A = IsSetDefault ? fDefaultAValue : appAtof(*afterTempStr);
	aReturn = FString::Printf(TEXT("(R=%f,G=%f,B=%f,A=%f)"), R * fMulti, G * fMulti, B * fMulti, A);

	return aReturn;
}

void PostExportPropertyInStruct(UObject* InObj, FString& ValueStr, const FString& StructValue, INT& Count, TCHAR StructStartChar, TCHAR StructEndChar)
{
	if (InObj->IsA(APostProcessVolume::StaticClass()))
	{
		if (StructValue == TEXT("PostProcessSettings"))
		{
			APostProcessVolume * aPostProcessVolume = Cast<APostProcessVolume>(InObj);
			if (aPostProcessVolume)
			{
				if (aPostProcessVolume->Settings.bOverride_EnableDOF && aPostProcessVolume->Settings.bEnableDOF)
				{
					Count++;
					if (Count == 1)
					{
						ValueStr += StructStartChar;
					}
					else
					{
						ValueStr += TEXT(",");
					}

					//DOF
					ValueStr += FString::Printf(TEXT("bOverride_DepthOfFieldFarTransitionRegion=True,"));
					ValueStr += FString::Printf(TEXT("DepthOfFieldFarTransitionRegion=%f,"), aPostProcessVolume->Settings.DOF_FocusInnerRadius);
					ValueStr += FString::Printf(TEXT("bOverride_DepthOfFieldFocalDistance=True,"));
					ValueStr += FString::Printf(TEXT("DepthOfFieldFocalDistance=0.0,"));
					ValueStr += FString::Printf(TEXT("bOverride_MotionBlurAmount=True,bOverride_MotionBlurMax=True,"));
					ValueStr += FString::Printf(TEXT("bOverride_ColorSaturation=True,"));
					ValueStr += FString::Printf(TEXT("bOverride_ColorGain=True"));
				}
			}
		}
	}
}

//2018-01-19 shimwoosung 특정 컴포넌트의 필드이름을 수정한다.
FString	RenamePropertyNameAndModValue(UObject* InObj, const FString& InPropName, FString& Value, FString* pStructValue)
{
	FString strReturn = InPropName;
	float fValue;

	if (InObj->IsA(UBrushComponent::StaticClass()))
	{
		if (InPropName == TEXT("BrushAggGeom"))
		{
			strReturn = TEXT("AggGeom");
		}
	}

	if (InObj->IsA(APostProcessVolume::StaticClass()))
	{
		if (pStructValue && *pStructValue == TEXT("PostProcessSettings") )
		{
			APostProcessVolume * aPostProcessVolume = Cast<APostProcessVolume>(InObj);
			if (InPropName == TEXT("Bloom_Scale"))
			{
				strReturn = TEXT("BloomIntensity");				
			}

			if (InPropName == TEXT("Bloom_Threshold"))
			{
				strReturn = TEXT("BloomThreshold");				
			}

			if (InPropName == TEXT("Bloom_Tint"))
			{
				strReturn = TEXT("Bloom2Tint");
				Value = ConvertLinearColor(Value, 1.0f, 1.0f, true);					
			}

			if (InPropName == TEXT("DOF_FocusType"))
			{
				strReturn = TEXT("DepthOfFieldMethod");
				Value = TEXT("DOFM_Gaussian");
			}

			if (InPropName == TEXT("DOF_FocusInnerRadius"))
			{
				strReturn = TEXT("DepthOfFieldFocalRegion");				
			}

			//MotionBlur
			if (InPropName == TEXT("MotionBlur_Amount"))
			{
				strReturn = TEXT("MotionBlurAmount");				
				if (!aPostProcessVolume->Settings.bOverride_EnableMotionBlur || !aPostProcessVolume->Settings.bEnableMotionBlur)
				{
					fValue = 0.0f;
					Value = FString::Printf(TEXT("%f"), fValue);
				}
			}

			if (InPropName == TEXT("MotionBlur_MaxVelocity"))
			{
				strReturn = TEXT("MotionBlurMax");				
			}

			if (InPropName == TEXT("Scene_Colorize"))
			{
				strReturn = TEXT("ColorSaturation");
				float X = 1.0f;
				float Y = 1.0f;
				float Z = 1.0f;
				float W = 1.0f;

				if (aPostProcessVolume->Settings.bOverride_Scene_Colorize)
				{
					X = aPostProcessVolume->Settings.Scene_Colorize.X;
					Y = aPostProcessVolume->Settings.Scene_Colorize.Y;
					Z = aPostProcessVolume->Settings.Scene_Colorize.Z;
				}

				if (aPostProcessVolume->Settings.bOverride_Scene_Desaturation)
				{
					W = 1.0f - aPostProcessVolume->Settings.Scene_Desaturation;
				}

				Value = FString::Printf(TEXT("(X=%f,Y=%f,Z=%f,W=%f)"), X, Y, Z, W);
			}

			if (InPropName == TEXT("Scene_HighLights"))
			{
				strReturn = TEXT("ColorGain");
				float X = 1.0f;
				float Y = 1.0f;
				float Z = 1.0f;
				float W = 1.0f;

				if (aPostProcessVolume->Settings.bOverride_Scene_HighLights)
				{
					X = 1.0f / aPostProcessVolume->Settings.Scene_HighLights.X;
					Y = 1.0f / aPostProcessVolume->Settings.Scene_HighLights.Y;
					Z = 1.0f / aPostProcessVolume->Settings.Scene_HighLights.Z;
				}
				
				Value = FString::Printf(TEXT("(X=%f,Y=%f,Z=%f,W=%f)"), X, Y, Z, W);
			}

			if (InPropName == TEXT("bOverride_Scene_ColorGradingLUT"))
			{
				strReturn = TEXT("bOverride_ColorGradingLUT");
			}

			if (InPropName == TEXT("ColorGrading_LookupTable"))
			{
				strReturn = TEXT("ColorGradingLUT");
			}
		}		
	}
	
	if (InObj->IsA(UPointLightComponent::StaticClass()))
	{
		if (InPropName == TEXT("Radius"))
		{
			strReturn = TEXT("AttenuationRadius");
		}

		if (InPropName == TEXT("FalloffExponent"))
		{
			strReturn = TEXT("LightFalloffExponent");
		}
	}

	if (InObj->IsA(ULightComponent::StaticClass()))
	{
		if (InPropName == TEXT("Brightness"))
		{
			strReturn = TEXT("Intensity");
			fValue = appAtof(*Value) * 5.0f;
			Value = FString::Printf(TEXT("%f"), fValue);
		}		
	}

	if (InObj->IsA(UExponentialHeightFogComponent::StaticClass()))
	{
		float fMultiValue = 1.0f;
		UExponentialHeightFogComponent * aFogCom = Cast<UExponentialHeightFogComponent>(InObj);
		
		if (InPropName == TEXT("OppositeLightColor"))
		{
			fMultiValue = aFogCom->OppositeLightBrightness;
			strReturn = TEXT("FogInscatteringColor");
			Value = ConvertLinearColor(Value, fMultiValue, 1.0f, true);
		}

		if (InPropName == TEXT("LightInscatteringColor"))
		{
			fMultiValue = aFogCom->LightInscatteringBrightness;
			strReturn = TEXT("DirectionalInscatteringColor");
			Value = ConvertLinearColor(Value, fMultiValue, 1.0f, true);
		}
	}

	if (InObj->IsA(AInfo::StaticClass()))
	{
		FString aName = InObj->GetName();
		
		if (aName.InStr(TEXT("ExponentialHeightFog")) != -1)
		{
			if (InPropName == TEXT("Component"))
			{
				TArray<UComponent*> Components;
				InObj->CollectComponents(Components, TRUE);
				for (INT CompIndex = Components.Num() - 1; CompIndex >= 0; --CompIndex)
				{
					UComponent * aCom = Components(CompIndex);					
					if (aCom->IsA(UExponentialHeightFogComponent::StaticClass()))
					{
						Value = aCom->GetInstanceMapName().ToString();
					}
				}
			}
		}
	}

	if (InObj->IsA(UBrushComponent::StaticClass()))
	{
		if (InPropName == TEXT("Name"))
		{
			UComponent * aCom = Cast<UComponent>(InObj);
			if (aCom)
			{
				UObject* aOuter = InObj->GetOuter();
				if (aOuter && aOuter->IsA(AVolume::StaticClass()))
				{
					Value = FString::Printf(TEXT("\"%s\""), *(aCom->GetInstanceMapName().ToString()));
				}
			}
		}		
	}

	if (InObj->IsA(AVolume::StaticClass()))
	{
		BOOL IsGetBrushComponent = FALSE;
		if (InPropName == TEXT("BrushComponent") )
		{
			IsGetBrushComponent = TRUE;
		} 

		if (InPropName == TEXT("CollisionComponent") )
		{
			IsGetBrushComponent = TRUE;
			strReturn = TEXT("RootComponent");
		}
		
		if (IsGetBrushComponent)
		{
			TArray<UComponent*> Components;
			InObj->CollectComponents(Components, TRUE);

			for (INT CompIndex = Components.Num() - 1; CompIndex >= 0; --CompIndex)
			{
				UComponent * aCom = Components(CompIndex);
				if (aCom->IsA(UBrushComponent::StaticClass()))
				{
					Value = FString::Printf(TEXT("BrushComponent\'%s\'"), *(aCom->GetInstanceMapName().ToString()));
				}
			}
		}
	}

	if (InObj->IsA(USkeletalMesh::StaticClass()))
	{
		USkeletalMesh * aSkeletalMesh = Cast<USkeletalMesh>(InObj);
		
		if (InPropName == TEXT("Origin"))
		{
			strReturn = TEXT("ImportTranslation");			
		}

		if (InPropName == TEXT("RotOrigin"))
		{
			FLOAT YawDeg = aSkeletalMesh->RotOrigin.Yaw * 0.00549316540360483;
			FLOAT PitchDeg = aSkeletalMesh->RotOrigin.Pitch * 0.00549316540360483;
			FLOAT RollDeg = aSkeletalMesh->RotOrigin.Roll * 0.00549316540360483;

			strReturn = TEXT("ImportRotation");
			Value = FString::Printf(TEXT("(Pitch=%6f,Yaw=%6f,Roll=%6f)"), PitchDeg, YawDeg, RollDeg);
		}
	}
	
	return strReturn;
}

FString	RenamePropertyNameAndBaseValue(UObject* InObj, const FString& InPropName, FString& Value, FString* pStructValue)
{
	FString strReturn = TEXT("");
	float fValue;

	if (InObj->IsA(UPointLightComponent::StaticClass()))
	{
		if (InPropName == TEXT("Radius"))
		{
			strReturn = TEXT("AttenuationRadius");
			fValue = 1024.0f;
			Value = FString::Printf(TEXT("%f"), fValue);
		}

		if (InPropName == TEXT("FalloffExponent"))
		{
			strReturn = TEXT("LightFalloffExponent");
			fValue = 2.0f;
			Value = FString::Printf(TEXT("%f"), fValue);
		}
	}

	if (InObj->IsA(ULightComponent::StaticClass()))
	{
		if (InPropName == TEXT("Brightness"))
		{
			strReturn = TEXT("Intensity");
			fValue = 5.0f;
			Value = FString::Printf(TEXT("%f"), fValue);
		}
	}

	if (InObj->IsA(APostProcessVolume::StaticClass()))
	{
		if (pStructValue && *pStructValue == TEXT("PostProcessSettings"))
		{
			APostProcessVolume * aPostProcessVolume = Cast<APostProcessVolume>(InObj);
			//Bloom
			if (InPropName == TEXT("bOverride_EnableBloom"))
			{
				if (aPostProcessVolume->Settings.bOverride_EnableBloom && aPostProcessVolume->Settings.bEnableBloom)
				{
					strReturn = TEXT("bOverride_BloomMethod");
					Value = TEXT("True");
				}
			}

			if (InPropName == TEXT("bOverride_Bloom_Scale"))
			{
				if (aPostProcessVolume->Settings.bOverride_Bloom_Scale)
				{
					strReturn = TEXT("bOverride_BloomIntensity");
					Value = TEXT("True");
				}
			}

			if (InPropName == TEXT("bOverride_Bloom_Threshold"))
			{
				if (aPostProcessVolume->Settings.bOverride_Bloom_Threshold)
				{
					strReturn = TEXT("bOverride_BloomThreshold");
					Value = TEXT("True");
				}
			}

			if (InPropName == TEXT("bOverride_Bloom_Tint"))
			{
				if (aPostProcessVolume->Settings.bOverride_Bloom_Tint)
				{
					strReturn = TEXT("bOverride_Bloom2Tint");
					Value = TEXT("True");
				}
			}

			if (InPropName == TEXT("Bloom_Scale"))
			{
				strReturn = TEXT("BloomIntensity");
				fValue = 1.0f;
				Value = FString::Printf(TEXT("%f"), fValue);
			}

			if (InPropName == TEXT("Bloom_Threshold"))
			{
				strReturn = TEXT("BloomThreshold");
				fValue = 1.0f;
				Value = FString::Printf(TEXT("%f"), fValue);
			}

			if (InPropName == TEXT("Bloom_Tint"))
			{
				strReturn = TEXT("Bloom2Tint");				
				Value = TEXT("(R=1.00,G=1.00,B=1.00,A=1.00)");
			}

			//DOF
			if (InPropName == TEXT("bOverride_EnableDOF"))
			{
				if (aPostProcessVolume->Settings.bOverride_EnableDOF && aPostProcessVolume->Settings.bEnableDOF)
				{
					strReturn = TEXT("bOverride_DepthOfFieldMethod");
					Value = TEXT("True");
				}
			}

			if (InPropName == TEXT("bOverride_DOF_FocusInnerRadius"))
			{
				if (aPostProcessVolume->Settings.bOverride_DOF_FocusInnerRadius)
				{
					strReturn = TEXT("bOverride_DepthOfFieldFocalRegion");
					Value = TEXT("True");
				}
			}			

			if (InPropName == TEXT("DOF_FocusType"))
			{
				strReturn = TEXT("DepthOfFieldMethod");
				Value = TEXT("DOFM_Gaussian");
			}

			if (InPropName == TEXT("DOF_FocusInnerRadius"))
			{
				strReturn = TEXT("DepthOfFieldFocalRegion");
				fValue = 2000.0f;
				Value = FString::Printf(TEXT("%f"), fValue);
			}	

			//MotionBlur
			if (InPropName == TEXT("MotionBlur_Amount"))
			{
				strReturn = TEXT("MotionBlurAmount");
				if (!aPostProcessVolume->Settings.bOverride_EnableMotionBlur || !aPostProcessVolume->Settings.bEnableMotionBlur)
				{
					fValue = 0.0f;
					Value = FString::Printf(TEXT("%f"), fValue);
				}
				else
				{
					fValue = 0.5f;
					Value = FString::Printf(TEXT("%f"), fValue);
				}
			}

			if (InPropName == TEXT("MotionBlur_MaxVelocity"))
			{
				strReturn = TEXT("MotionBlurMax");
				fValue = 1.0f;
				Value = FString::Printf(TEXT("%f"), fValue);
			}

			if (InPropName == TEXT("Scene_Colorize"))
			{
				strReturn = TEXT("ColorSaturation");
				float X = 1.0f;
				float Y = 1.0f;
				float Z = 1.0f;
				float W = 1.0f;

				if (aPostProcessVolume->Settings.bOverride_Scene_Colorize)
				{
					X = aPostProcessVolume->Settings.Scene_Colorize.X;
					Y = aPostProcessVolume->Settings.Scene_Colorize.Y;
					Z = aPostProcessVolume->Settings.Scene_Colorize.Z;
				}

				if (aPostProcessVolume->Settings.bOverride_Scene_Desaturation)
				{
					W = 1.0f - aPostProcessVolume->Settings.Scene_Desaturation;
				}

				Value = FString::Printf(TEXT("(X=%f,Y=%f,Z=%f,W=%f)"), X, Y, Z, W);
			}

			if (InPropName == TEXT("Scene_HighLights"))
			{
				strReturn = TEXT("ColorGain");
				float X = 1.0f;
				float Y = 1.0f;
				float Z = 1.0f;
				float W = 1.0f;

				if (aPostProcessVolume->Settings.bOverride_Scene_HighLights)
				{
					X = 1.0f / aPostProcessVolume->Settings.Scene_HighLights.X;
					Y = 1.0f / aPostProcessVolume->Settings.Scene_HighLights.Y;
					Z = 1.0f / aPostProcessVolume->Settings.Scene_HighLights.Z;
				}

				Value = FString::Printf(TEXT("(X=%f,Y=%f,Z=%f,W=%f)"), X, Y, Z, W);
			}
		}
	}

	return strReturn;
}

// UE4 导出相关全局变量定义
UBOOL GExportForUE4 = FALSE;
UBOOL GExportUntypedBulkDataForUE4 = FALSE;
UObject* GObjectExportForUE4 = NULL;

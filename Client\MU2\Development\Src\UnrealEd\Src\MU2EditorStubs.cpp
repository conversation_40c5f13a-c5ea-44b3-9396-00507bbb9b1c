// MU2EditorStubs.cpp
// 缺失的编辑器类的存根实现，用于解决链接错误

#include "UnrealEd.h"

// MU2DropItemPage 存根实现
class MU2DropItemPage
{
public:
    MU2DropItemPage() {}
    virtual ~MU2DropItemPage() {}
    
    // 添加必要的虚函数存根
    virtual void OnSize(int width, int height) {}
    virtual void OnPaint() {}
    virtual void OnCommand(int id) {}
};

// MU2PropertyPage 存根实现  
class MU2PropertyPage
{
public:
    MU2PropertyPage() {}
    virtual ~MU2PropertyPage() {}
    
    // 添加必要的虚函数存根
    virtual void OnSize(int width, int height) {}
    virtual void OnPaint() {}
    virtual void OnCommand(int id) {}
};

// MU2CapturePage 存根实现
class MU2CapturePage
{
public:
    MU2CapturePage() {}
    virtual ~MU2CapturePage() {}
    
    // 添加必要的虚函数存根
    virtual void OnSize(int width, int height) {}
    virtual void OnPaint() {}
    virtual void OnCommand(int id) {}
};

// WxMU2VolumeManager 存根实现
class WxMU2VolumeManager
{
public:
    WxMU2VolumeManager() {}
    virtual ~WxMU2VolumeManager() {}
    
    // 添加必要的方法存根
    virtual void Initialize() {}
    virtual void Shutdown() {}
    virtual void Update() {}
    
    // 静态实例方法
    static WxMU2VolumeManager* GetInstance()
    {
        static WxMU2VolumeManager instance;
        return &instance;
    }
};

// 导出必要的符号以解决链接错误
extern "C" 
{
    // MU2DropItemPage 相关符号
    void* MU2DropItemPage_Constructor() { return new MU2DropItemPage(); }
    void MU2DropItemPage_Destructor(void* obj) { delete static_cast<MU2DropItemPage*>(obj); }
    
    // MU2PropertyPage 相关符号
    void* MU2PropertyPage_Constructor() { return new MU2PropertyPage(); }
    void MU2PropertyPage_Destructor(void* obj) { delete static_cast<MU2PropertyPage*>(obj); }
    
    // MU2CapturePage 相关符号
    void* MU2CapturePage_Constructor() { return new MU2CapturePage(); }
    void MU2CapturePage_Destructor(void* obj) { delete static_cast<MU2CapturePage*>(obj); }
    
    // WxMU2VolumeManager 相关符号
    void* WxMU2VolumeManager_GetInstance() { return WxMU2VolumeManager::GetInstance(); }
    void WxMU2VolumeManager_Initialize(void* obj) { static_cast<WxMU2VolumeManager*>(obj)->Initialize(); }
    void WxMU2VolumeManager_Shutdown(void* obj) { static_cast<WxMU2VolumeManager*>(obj)->Shutdown(); }
    void WxMU2VolumeManager_Update(void* obj) { static_cast<WxMU2VolumeManager*>(obj)->Update(); }
}

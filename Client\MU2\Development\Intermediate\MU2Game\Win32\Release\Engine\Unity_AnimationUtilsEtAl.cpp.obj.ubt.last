C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\Hostx86\x86\cl.exe
 /nologo /Oi /Zp4 /Gy /fp:fast /c /Zm1100 /DYNAMICBASE:no /FS /bigobj /wd4819 /Od /Os /Ob2 /arch:SSE2 /errorReport:prompt /EHsc /Zi /MD /I "Core/Inc/Epic" /I "Core/Inc/Licensee" /I "Core/Inc" /I "Engine/Inc" /I "GameFramework/Inc" /I "IpDrv/Inc" /I "UnrealEd/Inc" /I "UnrealEd/FaceFX" /I "UnrealEdCLR/Inc" /I "UnrealSwarm/Inc" /I "GwNavEngine/Inc" /I "GwNavEditor/Inc" /I "../External/gwnav/3rd/tbb41_20121003oss/include" /I "../External/tinyXML" /I "../External/SpeedTree/Include" /I "GFxUI/Inc" /I "WinDrv/Inc" /I "D3D9Drv/Inc" /I "D3D11Drv/Inc" /I "XAudio2/Inc" /I "Launch/Inc" /I "../Tools/UnrealLightmass/Public" /I "$(CommonProgramFiles)" /I "OnlineSubsystemSteamworks/Inc" /I "../External/wxWidgets/include" /I "../External/wxWidgets/lib/vc_dll" /I "../External/wxExtended/wxDockit/include" /I "../External/Simplygon/Inc" /I "GFxUIEditor/Inc" /I "Engine\Src" /I "../External/gwnav/sdk/include" /I "../External/ChromiumEmbeddedFramework" /I "../External/PhysX/SDKs/Foundation/include" /I "../External/PhysX/SDKs/Physics/include" /I "../External/PhysX/SDKs/Physics/include/fluids" /I "../External/PhysX/SDKs/Physics/include/softbody" /I "../External/PhysX/SDKs/Physics/include/cloth" /I "../External/PhysX/SDKs/Cooking/include" /I "../External/PhysX/SDKs/PhysXLoader/include" /I "../External/PhysX/SDKs/PhysXExtensions/include" /I "../External/PhysX/SDKs/TetraMaker/NxTetra" /I "../External/PhysX/Nxd/include" /I "../External/PhysX-3/Include" /I "../External/PhysX/APEX/shared/general/shared" /I "../External/PhysX/APEX/public" /I "../External/PhysX/APEX/framework/public" /I "../External/PhysX/APEX/framework/public/PhysX2" /I "../External/PhysX/APEX/NxParameterized/public" /I "../External/PhysX/APEX/EditorWidgets" /I "../External/PhysX/APEX/module/clothing/public" /I "../External/PhysX/APEX/module/destructible/public" /I "../External/PhysX/APEX/module/emitter/public" /I "../External/PhysX/APEX/module/explosion/public" /I "../External/PhysX/APEX/module/forcefield/public" /I "../External/PhysX/APEX/module/iofx/public" /I "../External/PhysX/APEX/module/basicios/public" /I "../External/PhysX/APEX/module/particles/public" /I "../External/GFx/Include" /I "../External/GFx/Src" /I "../External/GFx/Src/GFx" /I "../External/GFx/Src/GFx/AMP" /I "../External/GFx/Src/GFx/AS2" /I "../External/GFx/Src/GFx/AS3" /I "../External/GFx/Src/GFx/Audio" /I "../External/GFx/Src/GFx/IME" /I "../External/GFx/Src/GFx/Text" /I "../External/GFx/Src/GFx/XML" /I "../External/GFx/Src/Kernel" /I "../External/GFx/Src/Render" /I "../External/GFx/Src/Render/ImageFiles" /I "../External/Recast/UE3/include" /I "../External/Steamworks/sdk/public" /I "../External/libPNG" /I "../External/libJPG" /I "../External/DirectShow/DirectShow" /I "../External/nvapi" /I "../External/nvtesslib/inc" /I "../External/lzopro/include" /I "../External/zlib/inc" /I "../External/FBX/2017.0.1/include" /I "../External/FBX/2017.0.1/include/fbxsdk" /I "../External/EasyHook" /I "$(DXSDK_DIR)/include" /I "../External/nvTextureTools-2.0.6/src/src" /I "../External/kiss_fft129" /I "../External/libogg-1.2.2/include" /I "../External/libvorbis-1.3.2/include" /I "../External/GFx/3rdParty/fmod/pc/Win32/inc" /D "UNICODE" /D "_UNICODE" /D "__UNREAL__" /D "WITH_GWNAV" /D "KY_BUILD_RELEASE" /D "WITH_FACEFX=0" /D "USE_BINK_CODEC=0" /D "WITH_SUBSTANCE_AIR=0" /D "WITH_GFx=1" /D "WITH_GFx_AUDIO=1" /D "WITH_GFx_VIDEO=1" /D "WITH_GFx_IME=1" /D "WITH_RECAST=1" /D "WITH_STEAMWORKS=1" /D "WITH_GAMECENTER=0" /D "WITH_PANORAMA=0" /D "WITH_UE3_NETWORKING=1" /D "UE3_LEAN_AND_MEAN=0" /D "WITH_EDITOR=1" /D "WITH_PERFORCE=1" /D "PX_NO_WARNING_PRAGMAS" /D "USE_SCOPED_MEM_STATS=1" /D "WJC_VS2022=1" /D "WJC_VS2015=0" /D "WJC_VS2013=0" /D "WJC_FBX2015=1" /D "WINDOWS_IGNORE_PACKING_MISMATCH=1" /D "_SILENCE_CXX17_ITERATOR_BASE_CLASS_DEPRECATION_WARNING=1" /D "_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS=1" /D "_WINDOWS=1" /D "WIN32=1" /D "_WIN32_WINNT=0x0502" /D "WINVER=0x0502" /D "WITH_STEAMWORKS=1" /D "DEDICATED_SERVER=0" /D "PNG_NO_FLOATING_POINT_SUPPORTED=1" /D "WITH_JPEG=1" /D "WXUSINGDLL" /D "wxUSE_NO_MANIFEST" /D "WITH_FBX=1" /D "FBXSDK_NEW_API" /D "USE_DYNAMIC_ES2_RHI=0" /D "WITH_KISSFFT=1" /D "XDKINSTALLED=0" /D "WITH_SPEEDTREE=0" /D "DWTRIOVIZSDK=0" /D "WITH_SIMPLYGON_DLL=1" /D "WITH_SIMPLYGON=1" /D "WITH_OPEN_AUTOMATE=1" /D "WITH_MANAGED_CODE=1" /D "NDEBUG=1" /D "FINAL_RELEASE=0" /D "MU2_RELEASE" /Yu"EnginePrivate.h" /Fp"F:\Release_Branch\Client\MU2\Development\Intermediate\MU2Game\Win32\Release\Engine\EnginePrivate.h.pch" "F:\Release_Branch\Client\MU2\Development\Intermediate\MU2Game\Win32\Release\Engine\Unity_AnimationUtilsEtAl.cpp" /Fo"F:\Release_Branch\Client\MU2\Development\Intermediate\MU2Game\Win32\Release\Engine\Unity_AnimationUtilsEtAl.cpp.obj" /Fd"F:\Release_Branch\Client\MU2\Development\Intermediate\MU2Game\Win32\Release\Engine\EnginePrivate.h.pdb" /TP /GR- /W4 /permissive- /Zc:twoPhase- /Zc:strictStrings- /wd4996 /wd4244 /wd4305 /wd5205 /wd4121 /wd4596 /wd4471 /wd4644
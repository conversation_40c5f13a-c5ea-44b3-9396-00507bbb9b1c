{"RootPath": "G:\\游戏源码\\MU2 起源端全套\\MU2全套源码63G\\Release_Branch\\Release_Branch\\Client\\MU2\\Development\\Src\\UnrealBuildTool", "ProjectFileName": "UnrealBuildTool_2015.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "..\\IPhone\\UnrealBuildTool\\Scripts\\UE3BuildIPhone.cs"}, {"SourceFile": "..\\IPhone\\UnrealBuildTool\\System\\IPhoneToolChain.cs"}, {"SourceFile": "..\\IPhone\\UnrealBuildTool\\System\\XcodeToolChain.cs"}, {"SourceFile": "..\\Android\\UnrealBuildTool\\Scripts\\UE3BuildAndroid.cs"}, {"SourceFile": "..\\Android\\UnrealBuildTool\\System\\AndroidToolChain.cs"}, {"SourceFile": "Configuration\\UE3BuildFlashStub.cs"}, {"SourceFile": "ToolChain\\FlashToolChainStub.cs"}, {"SourceFile": "..\\Launch\\Resources\\MetaData.cs"}, {"SourceFile": "Configuration\\UE3BuildExoGame.cs"}, {"SourceFile": "Configuration\\UE3BuildMac.cs"}, {"SourceFile": "Configuration\\UE3BuildMU2Game.cs"}, {"SourceFile": "Configuration\\UE3BuildSwordGame.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Configuration\\DebugInfoHeuristic.cs"}, {"SourceFile": "Configuration\\UE3BuildConfiguration.cs"}, {"SourceFile": "Configuration\\UE3BuildExampleGame.cs"}, {"SourceFile": "Configuration\\UE3BuildExternal.cs"}, {"SourceFile": "Configuration\\UE3BuildTarget.cs"}, {"SourceFile": "Configuration\\UE3BuildUTGame.cs"}, {"SourceFile": "Configuration\\UE3BuildWin32.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "System\\ActionGraph.cs"}, {"SourceFile": "System\\BuildException.cs"}, {"SourceFile": "Configuration\\BuildConfiguration.cs"}, {"SourceFile": "System\\CPPEnvironment.cs"}, {"SourceFile": "System\\CPPHeaders.cs"}, {"SourceFile": "System\\DependencyCache.cs"}, {"SourceFile": "System\\DataBase.cs"}, {"SourceFile": "System\\FileItem.cs"}, {"SourceFile": "System\\RPCUtilHelper.cs"}, {"SourceFile": "ToolChain\\IntelToolChain.cs"}, {"SourceFile": "System\\LinkEnvironment.cs"}, {"SourceFile": "System\\LocalExecutor.cs"}, {"SourceFile": "System\\ResponseFile.cs"}, {"SourceFile": "System\\Unity.cs"}, {"SourceFile": "System\\UnrealBuildTool.cs"}, {"SourceFile": "System\\Utils.cs"}, {"SourceFile": "System\\VCProject.cs"}, {"SourceFile": "ToolChain\\MacToolChain.cs"}, {"SourceFile": "ToolChain\\VCToolChain.cs"}, {"SourceFile": "System\\XGE.cs"}, {"SourceFile": "ToolChain\\XcodeMacToolChain.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.5.1.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5.1\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "G:\\游戏源码\\MU2 起源端全套\\MU2全套源码63G\\Release_Branch\\Release_Branch\\Client\\MU2\\Binaries\\RPCUtility.exe", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5.1\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5.1\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5.1\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5.1\\System.Runtime.Remoting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.5.1\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "G:\\游戏源码\\MU2 起源端全套\\MU2全套源码63G\\Release_Branch\\Release_Branch\\Client\\MU2\\Development\\Intermediate\\UnrealBuildTool\\Debug\\UnrealBuildTool.exe", "OutputItemRelativePath": "UnrealBuildTool.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}